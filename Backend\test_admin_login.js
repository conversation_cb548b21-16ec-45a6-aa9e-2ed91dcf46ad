const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAdminLogin() {
  try {
    console.log('🧪 TESTING ADMIN LOGIN AND PROMOTION ACCESS\n');
    console.log('=' .repeat(50));
    
    // Step 1: Login as admin
    console.log('1️⃣ Attempting admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123' // You might need to adjust this password
    });
    
    if (loginResponse.status === 200) {
      console.log('✅ Admin login successful!');
      const token = loginResponse.data.Data.token;
      const user = loginResponse.data.Data.user;
      
      console.log(`👤 User: ${user.name} (${user.email})`);
      console.log(`🎭 Role: ${user.role}`);
      console.log(`🔑 Token: ${token.substring(0, 20)}...`);
      
      // Step 2: Test promotion API with admin token
      console.log('\n2️⃣ Testing promotion API with admin token...');
      const promotionResponse = await axios.get(`${BASE_URL}/promotions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (promotionResponse.status === 200) {
        console.log('✅ Promotion API call successful!');
        const promotions = promotionResponse.data.data;
        console.log(`📊 Total promotions: ${promotions.length}`);
        
        // Count by type
        const publicCount = promotions.filter(p => p.type === 'PUBLIC').length;
        const privateCount = promotions.filter(p => p.type === 'PRIVATE').length;
        
        console.log(`🌍 Public promotions: ${publicCount}`);
        console.log(`🔒 Private promotions: ${privateCount}`);
        
        if (privateCount > 0) {
          console.log('🎉 SUCCESS! Admin can see private promotions!');
        } else {
          console.log('⚠️  No private promotions found, but API is working');
        }
        
        // Show first few promotions
        console.log('\n📋 Sample promotions:');
        promotions.slice(0, 3).forEach((promo, index) => {
          console.log(`${index + 1}. ${promo.name} (${promo.type}) - ${promo.code}`);
        });
        
      } else {
        console.log('❌ Promotion API call failed');
      }
      
    } else {
      console.log('❌ Admin login failed');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Login failed - possible reasons:');
      console.log('   - Wrong password');
      console.log('   - Admin user not verified');
      console.log('   - Admin user is locked');
    }
  }
}

// Instructions
console.log('⚠️  Make sure backend server is running on port 5000');
console.log('💡 If login fails, you might need to check/update admin password\n');

testAdminLogin();
