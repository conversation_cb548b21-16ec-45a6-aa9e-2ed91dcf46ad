{"ast": null, "code": "import PromotionActions from \"./actions\";\nconst initialState = {\n  promotions: [],\n  selectedPromotion: null,\n  loading: false,\n  creating: false,\n  updating: false,\n  deleting: false,\n  error: null,\n  // Pagination\n  pagination: {\n    currentPage: 1,\n    totalPages: 1,\n    totalPromotions: 0,\n    limit: 10,\n    hasNextPage: false,\n    hasPrevPage: false\n  },\n  // Filters\n  filters: {\n    search: '',\n    status: 'all',\n    sortBy: 'status',\n    sortOrder: 'asc'\n  },\n  // Statistics\n  stats: {\n    total: 0,\n    active: 0,\n    upcoming: 0,\n    inactive: 0,\n    expired: 0\n  },\n  // Promotion User Management\n  promotionUsers: {\n    data: [],\n    loading: false,\n    error: null,\n    pagination: {\n      currentPage: 1,\n      totalPages: 1,\n      totalUsers: 0,\n      limit: 20,\n      hasNextPage: false,\n      hasPrevPage: false\n    },\n    filters: {\n      search: '',\n      status: 'all',\n      sortBy: 'claimedAt',\n      sortOrder: 'desc'\n    }\n  },\n  userPromotions: {\n    data: [],\n    user: null,\n    loading: false,\n    error: null,\n    pagination: {\n      currentPage: 1,\n      totalPages: 1,\n      totalPromotions: 0,\n      limit: 20,\n      hasNextPage: false,\n      hasPrevPage: false\n    }\n  },\n  // Loading states for user management actions\n  removingUser: false,\n  resettingUsage: false,\n  // Assign Promotion\n  assignUsers: {\n    availableUsers: [],\n    loading: false,\n    error: null,\n    pagination: {\n      currentPage: 1,\n      totalPages: 1,\n      totalUsers: 0,\n      limit: 20,\n      hasNextPage: false,\n      hasPrevPage: false\n    },\n    filters: {\n      search: ''\n    }\n  },\n  assigningUsers: false\n};\nconst promotionReducer = (state = initialState, action) => {\n  switch (action.type) {\n    // Fetch all promotions\n    case PromotionActions.FETCH_ALL_PROMOTIONS:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case PromotionActions.FETCH_ALL_PROMOTIONS_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        promotions: action.payload.promotions || action.payload,\n        pagination: {\n          ...state.pagination,\n          ...action.payload.pagination\n        },\n        stats: {\n          ...state.stats,\n          ...action.payload.stats\n        },\n        filters: {\n          ...state.filters,\n          ...action.payload.filters\n        },\n        error: null\n      };\n    case PromotionActions.FETCH_ALL_PROMOTIONS_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n\n    // Create promotion\n    case PromotionActions.CREATE_PROMOTION:\n      return {\n        ...state,\n        creating: true,\n        error: null\n      };\n    case PromotionActions.CREATE_PROMOTION_SUCCESS:\n      return {\n        ...state,\n        creating: false,\n        promotions: [action.payload, ...state.promotions],\n        totalCount: state.totalCount + 1,\n        error: null\n      };\n    case PromotionActions.CREATE_PROMOTION_FAILURE:\n      return {\n        ...state,\n        creating: false,\n        error: action.payload\n      };\n\n    // Update promotion\n    case PromotionActions.UPDATE_PROMOTION:\n      return {\n        ...state,\n        updating: true,\n        error: null\n      };\n    case PromotionActions.UPDATE_PROMOTION_SUCCESS:\n      return {\n        ...state,\n        updating: false,\n        promotions: state.promotions.map(promotion => promotion._id === action.payload._id ? action.payload : promotion),\n        selectedPromotion: action.payload,\n        error: null\n      };\n    case PromotionActions.UPDATE_PROMOTION_FAILURE:\n      return {\n        ...state,\n        updating: false,\n        error: action.payload\n      };\n\n    // Delete promotion\n    case PromotionActions.DELETE_PROMOTION:\n      return {\n        ...state,\n        deleting: true,\n        error: null\n      };\n    case PromotionActions.DELETE_PROMOTION_SUCCESS:\n      return {\n        ...state,\n        deleting: false,\n        promotions: state.promotions.filter(promotion => promotion._id !== action.payload.id),\n        totalCount: state.totalCount - 1,\n        error: null\n      };\n    case PromotionActions.DELETE_PROMOTION_FAILURE:\n      return {\n        ...state,\n        deleting: false,\n        error: action.payload\n      };\n\n    // Get promotion by ID\n    case PromotionActions.GET_PROMOTION_BY_ID:\n      return {\n        ...state,\n        loading: true,\n        error: null\n      };\n    case PromotionActions.GET_PROMOTION_BY_ID_SUCCESS:\n      return {\n        ...state,\n        loading: false,\n        selectedPromotion: action.payload,\n        error: null\n      };\n    case PromotionActions.GET_PROMOTION_BY_ID_FAILURE:\n      return {\n        ...state,\n        loading: false,\n        error: action.payload\n      };\n\n    // Toggle promotion status\n    case PromotionActions.TOGGLE_PROMOTION_STATUS:\n      return {\n        ...state,\n        updating: true,\n        error: null\n      };\n    case PromotionActions.TOGGLE_PROMOTION_STATUS_SUCCESS:\n      return {\n        ...state,\n        updating: false,\n        promotions: state.promotions.map(promotion => promotion._id === action.payload._id ? action.payload : promotion),\n        error: null\n      };\n    case PromotionActions.TOGGLE_PROMOTION_STATUS_FAILURE:\n      return {\n        ...state,\n        updating: false,\n        error: action.payload\n      };\n\n    // Clear error\n    case PromotionActions.CLEAR_PROMOTION_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n\n    // Set filters\n    case PromotionActions.SET_PROMOTION_FILTERS:\n      return {\n        ...state,\n        filters: {\n          ...state.filters,\n          ...action.payload\n        }\n      };\n\n    // Set pagination\n    case PromotionActions.SET_PROMOTION_PAGINATION:\n      return {\n        ...state,\n        pagination: {\n          ...state.pagination,\n          ...action.payload\n        }\n      };\n\n    // Reset filters\n    case PromotionActions.RESET_PROMOTION_FILTERS:\n      return {\n        ...state,\n        filters: {\n          search: '',\n          status: 'all',\n          sortBy: 'createdAt',\n          sortOrder: 'desc'\n        },\n        pagination: {\n          ...state.pagination,\n          currentPage: 1\n        }\n      };\n\n    // ===== PROMOTION USER MANAGEMENT =====\n\n    // Get promotion users\n    case PromotionActions.GET_PROMOTION_USERS:\n      return {\n        ...state,\n        promotionUsers: {\n          ...state.promotionUsers,\n          loading: true,\n          error: null\n        }\n      };\n    case PromotionActions.GET_PROMOTION_USERS_SUCCESS:\n      return {\n        ...state,\n        promotionUsers: {\n          ...state.promotionUsers,\n          loading: false,\n          data: action.payload.data.users || [],\n          pagination: {\n            ...state.promotionUsers.pagination,\n            ...action.payload.data.pagination\n          },\n          filters: {\n            ...state.promotionUsers.filters,\n            ...action.payload.data.filters\n          },\n          error: null\n        }\n      };\n    case PromotionActions.GET_PROMOTION_USERS_FAILURE:\n      return {\n        ...state,\n        promotionUsers: {\n          ...state.promotionUsers,\n          loading: false,\n          error: action.payload\n        }\n      };\n\n    // Get user promotions\n    case PromotionActions.GET_USER_PROMOTIONS:\n      return {\n        ...state,\n        userPromotions: {\n          ...state.userPromotions,\n          loading: true,\n          error: null\n        }\n      };\n    case PromotionActions.GET_USER_PROMOTIONS_SUCCESS:\n      return {\n        ...state,\n        userPromotions: {\n          ...state.userPromotions,\n          loading: false,\n          data: action.payload.data.promotions || [],\n          user: action.payload.data.user,\n          pagination: {\n            ...state.userPromotions.pagination,\n            ...action.payload.data.pagination\n          },\n          error: null\n        }\n      };\n    case PromotionActions.GET_USER_PROMOTIONS_FAILURE:\n      return {\n        ...state,\n        userPromotions: {\n          ...state.userPromotions,\n          loading: false,\n          error: action.payload\n        }\n      };\n\n    // Remove user from promotion\n    case PromotionActions.REMOVE_USER_FROM_PROMOTION:\n      return {\n        ...state,\n        removingUser: true,\n        error: null\n      };\n    case PromotionActions.REMOVE_USER_FROM_PROMOTION_SUCCESS:\n      return {\n        ...state,\n        removingUser: false,\n        // Remove user from promotionUsers data\n        promotionUsers: {\n          ...state.promotionUsers,\n          data: state.promotionUsers.data.filter(user => user._id !== action.payload.removedUserId)\n        },\n        error: null\n      };\n    case PromotionActions.REMOVE_USER_FROM_PROMOTION_FAILURE:\n      return {\n        ...state,\n        removingUser: false,\n        error: action.payload\n      };\n\n    // Reset user promotion usage\n    case PromotionActions.RESET_USER_PROMOTION_USAGE:\n      return {\n        ...state,\n        resettingUsage: true,\n        error: null\n      };\n    case PromotionActions.RESET_USER_PROMOTION_USAGE_SUCCESS:\n      return {\n        ...state,\n        resettingUsage: false,\n        // Update user in promotionUsers data\n        promotionUsers: {\n          ...state.promotionUsers,\n          data: state.promotionUsers.data.map(user => user._id === action.payload.data.promotionUser._id ? {\n            ...user,\n            usedCount: 0,\n            lastUsedAt: null\n          } : user)\n        },\n        error: null\n      };\n    case PromotionActions.RESET_USER_PROMOTION_USAGE_FAILURE:\n      return {\n        ...state,\n        resettingUsage: false,\n        error: action.payload\n      };\n\n    // ===== ASSIGN PROMOTION =====\n\n    // Search users for assignment\n    case PromotionActions.SEARCH_USERS_FOR_ASSIGNMENT:\n      return {\n        ...state,\n        assignUsers: {\n          ...state.assignUsers,\n          loading: true,\n          error: null\n        }\n      };\n    case PromotionActions.SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS:\n      return {\n        ...state,\n        assignUsers: {\n          ...state.assignUsers,\n          loading: false,\n          availableUsers: action.payload.data.users || [],\n          pagination: {\n            ...state.assignUsers.pagination,\n            ...action.payload.data.pagination\n          },\n          filters: {\n            ...state.assignUsers.filters,\n            ...action.payload.data.filters\n          },\n          error: null\n        }\n      };\n    case PromotionActions.SEARCH_USERS_FOR_ASSIGNMENT_FAILURE:\n      return {\n        ...state,\n        assignUsers: {\n          ...state.assignUsers,\n          loading: false,\n          error: action.payload\n        }\n      };\n\n    // Assign promotion to users\n    case PromotionActions.ASSIGN_PROMOTION_TO_USERS:\n      return {\n        ...state,\n        assigningUsers: true,\n        error: null\n      };\n    case PromotionActions.ASSIGN_PROMOTION_TO_USERS_SUCCESS:\n      return {\n        ...state,\n        assigningUsers: false,\n        // Update available users to reflect assignment status\n        assignUsers: {\n          ...state.assignUsers,\n          availableUsers: state.assignUsers.availableUsers.map(user => {\n            const wasAssigned = action.payload.data.assignedUsers.some(assignedUser => assignedUser._id === user._id);\n            return wasAssigned ? {\n              ...user,\n              isAssigned: true\n            } : user;\n          })\n        },\n        error: null\n      };\n    case PromotionActions.ASSIGN_PROMOTION_TO_USERS_FAILURE:\n      return {\n        ...state,\n        assigningUsers: false,\n        error: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport default promotionReducer;", "map": {"version": 3, "names": ["PromotionActions", "initialState", "promotions", "selectedPromotion", "loading", "creating", "updating", "deleting", "error", "pagination", "currentPage", "totalPages", "totalPromotions", "limit", "hasNextPage", "hasPrevPage", "filters", "search", "status", "sortBy", "sortOrder", "stats", "total", "active", "upcoming", "inactive", "expired", "promotionUsers", "data", "totalUsers", "userPromotions", "user", "removingUser", "resettingUsage", "assignUsers", "availableUsers", "assigningUsers", "promotionReducer", "state", "action", "type", "FETCH_ALL_PROMOTIONS", "FETCH_ALL_PROMOTIONS_SUCCESS", "payload", "FETCH_ALL_PROMOTIONS_FAILURE", "CREATE_PROMOTION", "CREATE_PROMOTION_SUCCESS", "totalCount", "CREATE_PROMOTION_FAILURE", "UPDATE_PROMOTION", "UPDATE_PROMOTION_SUCCESS", "map", "promotion", "_id", "UPDATE_PROMOTION_FAILURE", "DELETE_PROMOTION", "DELETE_PROMOTION_SUCCESS", "filter", "id", "DELETE_PROMOTION_FAILURE", "GET_PROMOTION_BY_ID", "GET_PROMOTION_BY_ID_SUCCESS", "GET_PROMOTION_BY_ID_FAILURE", "TOGGLE_PROMOTION_STATUS", "TOGGLE_PROMOTION_STATUS_SUCCESS", "TOGGLE_PROMOTION_STATUS_FAILURE", "CLEAR_PROMOTION_ERROR", "SET_PROMOTION_FILTERS", "SET_PROMOTION_PAGINATION", "RESET_PROMOTION_FILTERS", "GET_PROMOTION_USERS", "GET_PROMOTION_USERS_SUCCESS", "users", "GET_PROMOTION_USERS_FAILURE", "GET_USER_PROMOTIONS", "GET_USER_PROMOTIONS_SUCCESS", "GET_USER_PROMOTIONS_FAILURE", "REMOVE_USER_FROM_PROMOTION", "REMOVE_USER_FROM_PROMOTION_SUCCESS", "removedUserId", "REMOVE_USER_FROM_PROMOTION_FAILURE", "RESET_USER_PROMOTION_USAGE", "RESET_USER_PROMOTION_USAGE_SUCCESS", "promotionUser", "usedCount", "lastUsedAt", "RESET_USER_PROMOTION_USAGE_FAILURE", "SEARCH_USERS_FOR_ASSIGNMENT", "SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS", "SEARCH_USERS_FOR_ASSIGNMENT_FAILURE", "ASSIGN_PROMOTION_TO_USERS", "ASSIGN_PROMOTION_TO_USERS_SUCCESS", "wasAssigned", "assignedUsers", "some", "assignedUser", "isAssigned", "ASSIGN_PROMOTION_TO_USERS_FAILURE"], "sources": ["E:/Uroom/Admin/src/redux/promotion/reducer.js"], "sourcesContent": ["import PromotionActions from \"./actions\";\r\n\r\nconst initialState = {\r\n  promotions: [],\r\n  selectedPromotion: null,\r\n  loading: false,\r\n  creating: false,\r\n  updating: false,\r\n  deleting: false,\r\n  error: null,\r\n\r\n  // Pagination\r\n  pagination: {\r\n    currentPage: 1,\r\n    totalPages: 1,\r\n    totalPromotions: 0,\r\n    limit: 10,\r\n    hasNextPage: false,\r\n    hasPrevPage: false\r\n  },\r\n\r\n  // Filters\r\n  filters: {\r\n    search: '',\r\n    status: 'all',\r\n    sortBy: 'status',\r\n    sortOrder: 'asc'\r\n  },\r\n\r\n  // Statistics\r\n  stats: {\r\n    total: 0,\r\n    active: 0,\r\n    upcoming: 0,\r\n    inactive: 0,\r\n    expired: 0\r\n  },\r\n\r\n  // Promotion User Management\r\n  promotionUsers: {\r\n    data: [],\r\n    loading: false,\r\n    error: null,\r\n    pagination: {\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      totalUsers: 0,\r\n      limit: 20,\r\n      hasNextPage: false,\r\n      hasPrevPage: false\r\n    },\r\n    filters: {\r\n      search: '',\r\n      status: 'all',\r\n      sortBy: 'claimedAt',\r\n      sortOrder: 'desc'\r\n    }\r\n  },\r\n\r\n\r\n\r\n  userPromotions: {\r\n    data: [],\r\n    user: null,\r\n    loading: false,\r\n    error: null,\r\n    pagination: {\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      totalPromotions: 0,\r\n      limit: 20,\r\n      hasNextPage: false,\r\n      hasPrevPage: false\r\n    }\r\n  },\r\n\r\n  // Loading states for user management actions\r\n  removingUser: false,\r\n  resettingUsage: false,\r\n\r\n  // Assign Promotion\r\n  assignUsers: {\r\n    availableUsers: [],\r\n    loading: false,\r\n    error: null,\r\n    pagination: {\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      totalUsers: 0,\r\n      limit: 20,\r\n      hasNextPage: false,\r\n      hasPrevPage: false\r\n    },\r\n    filters: {\r\n      search: ''\r\n    }\r\n  },\r\n  assigningUsers: false\r\n};\r\n\r\nconst promotionReducer = (state = initialState, action) => {\r\n  switch (action.type) {\r\n    // Fetch all promotions\r\n    case PromotionActions.FETCH_ALL_PROMOTIONS:\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.FETCH_ALL_PROMOTIONS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        promotions: action.payload.promotions || action.payload,\r\n        pagination: {\r\n          ...state.pagination,\r\n          ...action.payload.pagination,\r\n        },\r\n        stats: {\r\n          ...state.stats,\r\n          ...action.payload.stats,\r\n        },\r\n        filters: {\r\n          ...state.filters,\r\n          ...action.payload.filters,\r\n        },\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.FETCH_ALL_PROMOTIONS_FAILURE:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Create promotion\r\n    case PromotionActions.CREATE_PROMOTION:\r\n      return {\r\n        ...state,\r\n        creating: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.CREATE_PROMOTION_SUCCESS:\r\n      return {\r\n        ...state,\r\n        creating: false,\r\n        promotions: [action.payload, ...state.promotions],\r\n        totalCount: state.totalCount + 1,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.CREATE_PROMOTION_FAILURE:\r\n      return {\r\n        ...state,\r\n        creating: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Update promotion\r\n    case PromotionActions.UPDATE_PROMOTION:\r\n      return {\r\n        ...state,\r\n        updating: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.UPDATE_PROMOTION_SUCCESS:\r\n      return {\r\n        ...state,\r\n        updating: false,\r\n        promotions: state.promotions.map((promotion) =>\r\n          promotion._id === action.payload._id ? action.payload : promotion\r\n        ),\r\n        selectedPromotion: action.payload,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.UPDATE_PROMOTION_FAILURE:\r\n      return {\r\n        ...state,\r\n        updating: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Delete promotion\r\n    case PromotionActions.DELETE_PROMOTION:\r\n      return {\r\n        ...state,\r\n        deleting: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.DELETE_PROMOTION_SUCCESS:\r\n      return {\r\n        ...state,\r\n        deleting: false,\r\n        promotions: state.promotions.filter(\r\n          (promotion) => promotion._id !== action.payload.id\r\n        ),\r\n        totalCount: state.totalCount - 1,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.DELETE_PROMOTION_FAILURE:\r\n      return {\r\n        ...state,\r\n        deleting: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Get promotion by ID\r\n    case PromotionActions.GET_PROMOTION_BY_ID:\r\n      return {\r\n        ...state,\r\n        loading: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.GET_PROMOTION_BY_ID_SUCCESS:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        selectedPromotion: action.payload,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.GET_PROMOTION_BY_ID_FAILURE:\r\n      return {\r\n        ...state,\r\n        loading: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Toggle promotion status\r\n    case PromotionActions.TOGGLE_PROMOTION_STATUS:\r\n      return {\r\n        ...state,\r\n        updating: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.TOGGLE_PROMOTION_STATUS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        updating: false,\r\n        promotions: state.promotions.map((promotion) =>\r\n          promotion._id === action.payload._id ? action.payload : promotion\r\n        ),\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.TOGGLE_PROMOTION_STATUS_FAILURE:\r\n      return {\r\n        ...state,\r\n        updating: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Clear error\r\n    case PromotionActions.CLEAR_PROMOTION_ERROR:\r\n      return {\r\n        ...state,\r\n        error: null,\r\n      };\r\n\r\n    // Set filters\r\n    case PromotionActions.SET_PROMOTION_FILTERS:\r\n      return {\r\n        ...state,\r\n        filters: {\r\n          ...state.filters,\r\n          ...action.payload,\r\n        },\r\n      };\r\n\r\n    // Set pagination\r\n    case PromotionActions.SET_PROMOTION_PAGINATION:\r\n      return {\r\n        ...state,\r\n        pagination: {\r\n          ...state.pagination,\r\n          ...action.payload,\r\n        },\r\n      };\r\n\r\n    // Reset filters\r\n    case PromotionActions.RESET_PROMOTION_FILTERS:\r\n      return {\r\n        ...state,\r\n        filters: {\r\n          search: '',\r\n          status: 'all',\r\n          sortBy: 'createdAt',\r\n          sortOrder: 'desc'\r\n        },\r\n        pagination: {\r\n          ...state.pagination,\r\n          currentPage: 1,\r\n        },\r\n      };\r\n\r\n    // ===== PROMOTION USER MANAGEMENT =====\r\n\r\n    // Get promotion users\r\n    case PromotionActions.GET_PROMOTION_USERS:\r\n      return {\r\n        ...state,\r\n        promotionUsers: {\r\n          ...state.promotionUsers,\r\n          loading: true,\r\n          error: null,\r\n        },\r\n      };\r\n\r\n    case PromotionActions.GET_PROMOTION_USERS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        promotionUsers: {\r\n          ...state.promotionUsers,\r\n          loading: false,\r\n          data: action.payload.data.users || [],\r\n          pagination: {\r\n            ...state.promotionUsers.pagination,\r\n            ...action.payload.data.pagination,\r\n          },\r\n          filters: {\r\n            ...state.promotionUsers.filters,\r\n            ...action.payload.data.filters,\r\n          },\r\n          error: null,\r\n        },\r\n      };\r\n\r\n    case PromotionActions.GET_PROMOTION_USERS_FAILURE:\r\n      return {\r\n        ...state,\r\n        promotionUsers: {\r\n          ...state.promotionUsers,\r\n          loading: false,\r\n          error: action.payload,\r\n        },\r\n      };\r\n\r\n    // Get user promotions\r\n    case PromotionActions.GET_USER_PROMOTIONS:\r\n      return {\r\n        ...state,\r\n        userPromotions: {\r\n          ...state.userPromotions,\r\n          loading: true,\r\n          error: null,\r\n        },\r\n      };\r\n\r\n    case PromotionActions.GET_USER_PROMOTIONS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        userPromotions: {\r\n          ...state.userPromotions,\r\n          loading: false,\r\n          data: action.payload.data.promotions || [],\r\n          user: action.payload.data.user,\r\n          pagination: {\r\n            ...state.userPromotions.pagination,\r\n            ...action.payload.data.pagination,\r\n          },\r\n          error: null,\r\n        },\r\n      };\r\n\r\n    case PromotionActions.GET_USER_PROMOTIONS_FAILURE:\r\n      return {\r\n        ...state,\r\n        userPromotions: {\r\n          ...state.userPromotions,\r\n          loading: false,\r\n          error: action.payload,\r\n        },\r\n      };\r\n\r\n    // Remove user from promotion\r\n    case PromotionActions.REMOVE_USER_FROM_PROMOTION:\r\n      return {\r\n        ...state,\r\n        removingUser: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.REMOVE_USER_FROM_PROMOTION_SUCCESS:\r\n      return {\r\n        ...state,\r\n        removingUser: false,\r\n        // Remove user from promotionUsers data\r\n        promotionUsers: {\r\n          ...state.promotionUsers,\r\n          data: state.promotionUsers.data.filter(\r\n            user => user._id !== action.payload.removedUserId\r\n          ),\r\n        },\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.REMOVE_USER_FROM_PROMOTION_FAILURE:\r\n      return {\r\n        ...state,\r\n        removingUser: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // Reset user promotion usage\r\n    case PromotionActions.RESET_USER_PROMOTION_USAGE:\r\n      return {\r\n        ...state,\r\n        resettingUsage: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.RESET_USER_PROMOTION_USAGE_SUCCESS:\r\n      return {\r\n        ...state,\r\n        resettingUsage: false,\r\n        // Update user in promotionUsers data\r\n        promotionUsers: {\r\n          ...state.promotionUsers,\r\n          data: state.promotionUsers.data.map(user =>\r\n            user._id === action.payload.data.promotionUser._id\r\n              ? { ...user, usedCount: 0, lastUsedAt: null }\r\n              : user\r\n          ),\r\n        },\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.RESET_USER_PROMOTION_USAGE_FAILURE:\r\n      return {\r\n        ...state,\r\n        resettingUsage: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    // ===== ASSIGN PROMOTION =====\r\n\r\n    // Search users for assignment\r\n    case PromotionActions.SEARCH_USERS_FOR_ASSIGNMENT:\r\n      return {\r\n        ...state,\r\n        assignUsers: {\r\n          ...state.assignUsers,\r\n          loading: true,\r\n          error: null,\r\n        },\r\n      };\r\n\r\n    case PromotionActions.SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS:\r\n      return {\r\n        ...state,\r\n        assignUsers: {\r\n          ...state.assignUsers,\r\n          loading: false,\r\n          availableUsers: action.payload.data.users || [],\r\n          pagination: {\r\n            ...state.assignUsers.pagination,\r\n            ...action.payload.data.pagination,\r\n          },\r\n          filters: {\r\n            ...state.assignUsers.filters,\r\n            ...action.payload.data.filters,\r\n          },\r\n          error: null,\r\n        },\r\n      };\r\n\r\n    case PromotionActions.SEARCH_USERS_FOR_ASSIGNMENT_FAILURE:\r\n      return {\r\n        ...state,\r\n        assignUsers: {\r\n          ...state.assignUsers,\r\n          loading: false,\r\n          error: action.payload,\r\n        },\r\n      };\r\n\r\n    // Assign promotion to users\r\n    case PromotionActions.ASSIGN_PROMOTION_TO_USERS:\r\n      return {\r\n        ...state,\r\n        assigningUsers: true,\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.ASSIGN_PROMOTION_TO_USERS_SUCCESS:\r\n      return {\r\n        ...state,\r\n        assigningUsers: false,\r\n        // Update available users to reflect assignment status\r\n        assignUsers: {\r\n          ...state.assignUsers,\r\n          availableUsers: state.assignUsers.availableUsers.map(user => {\r\n            const wasAssigned = action.payload.data.assignedUsers.some(\r\n              assignedUser => assignedUser._id === user._id\r\n            );\r\n            return wasAssigned ? { ...user, isAssigned: true } : user;\r\n          }),\r\n        },\r\n        error: null,\r\n      };\r\n\r\n    case PromotionActions.ASSIGN_PROMOTION_TO_USERS_FAILURE:\r\n      return {\r\n        ...state,\r\n        assigningUsers: false,\r\n        error: action.payload,\r\n      };\r\n\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\nexport default promotionReducer;\r\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,WAAW;AAExC,MAAMC,YAAY,GAAG;EACnBC,UAAU,EAAE,EAAE;EACdC,iBAAiB,EAAE,IAAI;EACvBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,IAAI;EAEX;EACAC,UAAU,EAAE;IACVC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACf,CAAC;EAED;EACAC,OAAO,EAAE;IACPC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC;EAED;EACAC,KAAK,EAAE;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,cAAc,EAAE;IACdC,IAAI,EAAE,EAAE;IACRxB,OAAO,EAAE,KAAK;IACdI,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE;MACVC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbkB,UAAU,EAAE,CAAC;MACbhB,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE;MACPC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,WAAW;MACnBC,SAAS,EAAE;IACb;EACF,CAAC;EAIDU,cAAc,EAAE;IACdF,IAAI,EAAE,EAAE;IACRG,IAAI,EAAE,IAAI;IACV3B,OAAO,EAAE,KAAK;IACdI,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE;MACVC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,CAAC;MAClBC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE;IACf;EACF,CAAC;EAED;EACAiB,YAAY,EAAE,KAAK;EACnBC,cAAc,EAAE,KAAK;EAErB;EACAC,WAAW,EAAE;IACXC,cAAc,EAAE,EAAE;IAClB/B,OAAO,EAAE,KAAK;IACdI,KAAK,EAAE,IAAI;IACXC,UAAU,EAAE;MACVC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbkB,UAAU,EAAE,CAAC;MACbhB,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,KAAK;MAClBC,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE;MACPC,MAAM,EAAE;IACV;EACF,CAAC;EACDmB,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,GAAGrC,YAAY,EAAEsC,MAAM,KAAK;EACzD,QAAQA,MAAM,CAACC,IAAI;IACjB;IACA,KAAKxC,gBAAgB,CAACyC,oBAAoB;MACxC,OAAO;QACL,GAAGH,KAAK;QACRlC,OAAO,EAAE,IAAI;QACbI,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC0C,4BAA4B;MAChD,OAAO;QACL,GAAGJ,KAAK;QACRlC,OAAO,EAAE,KAAK;QACdF,UAAU,EAAEqC,MAAM,CAACI,OAAO,CAACzC,UAAU,IAAIqC,MAAM,CAACI,OAAO;QACvDlC,UAAU,EAAE;UACV,GAAG6B,KAAK,CAAC7B,UAAU;UACnB,GAAG8B,MAAM,CAACI,OAAO,CAAClC;QACpB,CAAC;QACDY,KAAK,EAAE;UACL,GAAGiB,KAAK,CAACjB,KAAK;UACd,GAAGkB,MAAM,CAACI,OAAO,CAACtB;QACpB,CAAC;QACDL,OAAO,EAAE;UACP,GAAGsB,KAAK,CAACtB,OAAO;UAChB,GAAGuB,MAAM,CAACI,OAAO,CAAC3B;QACpB,CAAC;QACDR,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC4C,4BAA4B;MAChD,OAAO;QACL,GAAGN,KAAK;QACRlC,OAAO,EAAE,KAAK;QACdI,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAAC6C,gBAAgB;MACpC,OAAO;QACL,GAAGP,KAAK;QACRjC,QAAQ,EAAE,IAAI;QACdG,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC8C,wBAAwB;MAC5C,OAAO;QACL,GAAGR,KAAK;QACRjC,QAAQ,EAAE,KAAK;QACfH,UAAU,EAAE,CAACqC,MAAM,CAACI,OAAO,EAAE,GAAGL,KAAK,CAACpC,UAAU,CAAC;QACjD6C,UAAU,EAAET,KAAK,CAACS,UAAU,GAAG,CAAC;QAChCvC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACgD,wBAAwB;MAC5C,OAAO;QACL,GAAGV,KAAK;QACRjC,QAAQ,EAAE,KAAK;QACfG,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAACiD,gBAAgB;MACpC,OAAO;QACL,GAAGX,KAAK;QACRhC,QAAQ,EAAE,IAAI;QACdE,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACkD,wBAAwB;MAC5C,OAAO;QACL,GAAGZ,KAAK;QACRhC,QAAQ,EAAE,KAAK;QACfJ,UAAU,EAAEoC,KAAK,CAACpC,UAAU,CAACiD,GAAG,CAAEC,SAAS,IACzCA,SAAS,CAACC,GAAG,KAAKd,MAAM,CAACI,OAAO,CAACU,GAAG,GAAGd,MAAM,CAACI,OAAO,GAAGS,SAC1D,CAAC;QACDjD,iBAAiB,EAAEoC,MAAM,CAACI,OAAO;QACjCnC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACsD,wBAAwB;MAC5C,OAAO;QACL,GAAGhB,KAAK;QACRhC,QAAQ,EAAE,KAAK;QACfE,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAACuD,gBAAgB;MACpC,OAAO;QACL,GAAGjB,KAAK;QACR/B,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACwD,wBAAwB;MAC5C,OAAO;QACL,GAAGlB,KAAK;QACR/B,QAAQ,EAAE,KAAK;QACfL,UAAU,EAAEoC,KAAK,CAACpC,UAAU,CAACuD,MAAM,CAChCL,SAAS,IAAKA,SAAS,CAACC,GAAG,KAAKd,MAAM,CAACI,OAAO,CAACe,EAClD,CAAC;QACDX,UAAU,EAAET,KAAK,CAACS,UAAU,GAAG,CAAC;QAChCvC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC2D,wBAAwB;MAC5C,OAAO;QACL,GAAGrB,KAAK;QACR/B,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAAC4D,mBAAmB;MACvC,OAAO;QACL,GAAGtB,KAAK;QACRlC,OAAO,EAAE,IAAI;QACbI,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC6D,2BAA2B;MAC/C,OAAO;QACL,GAAGvB,KAAK;QACRlC,OAAO,EAAE,KAAK;QACdD,iBAAiB,EAAEoC,MAAM,CAACI,OAAO;QACjCnC,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC8D,2BAA2B;MAC/C,OAAO;QACL,GAAGxB,KAAK;QACRlC,OAAO,EAAE,KAAK;QACdI,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAAC+D,uBAAuB;MAC3C,OAAO;QACL,GAAGzB,KAAK;QACRhC,QAAQ,EAAE,IAAI;QACdE,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACgE,+BAA+B;MACnD,OAAO;QACL,GAAG1B,KAAK;QACRhC,QAAQ,EAAE,KAAK;QACfJ,UAAU,EAAEoC,KAAK,CAACpC,UAAU,CAACiD,GAAG,CAAEC,SAAS,IACzCA,SAAS,CAACC,GAAG,KAAKd,MAAM,CAACI,OAAO,CAACU,GAAG,GAAGd,MAAM,CAACI,OAAO,GAAGS,SAC1D,CAAC;QACD5C,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACiE,+BAA+B;MACnD,OAAO;QACL,GAAG3B,KAAK;QACRhC,QAAQ,EAAE,KAAK;QACfE,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAACkE,qBAAqB;MACzC,OAAO;QACL,GAAG5B,KAAK;QACR9B,KAAK,EAAE;MACT,CAAC;;IAEH;IACA,KAAKR,gBAAgB,CAACmE,qBAAqB;MACzC,OAAO;QACL,GAAG7B,KAAK;QACRtB,OAAO,EAAE;UACP,GAAGsB,KAAK,CAACtB,OAAO;UAChB,GAAGuB,MAAM,CAACI;QACZ;MACF,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAACoE,wBAAwB;MAC5C,OAAO;QACL,GAAG9B,KAAK;QACR7B,UAAU,EAAE;UACV,GAAG6B,KAAK,CAAC7B,UAAU;UACnB,GAAG8B,MAAM,CAACI;QACZ;MACF,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAACqE,uBAAuB;MAC3C,OAAO;QACL,GAAG/B,KAAK;QACRtB,OAAO,EAAE;UACPC,MAAM,EAAE,EAAE;UACVC,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE,WAAW;UACnBC,SAAS,EAAE;QACb,CAAC;QACDX,UAAU,EAAE;UACV,GAAG6B,KAAK,CAAC7B,UAAU;UACnBC,WAAW,EAAE;QACf;MACF,CAAC;;IAEH;;IAEA;IACA,KAAKV,gBAAgB,CAACsE,mBAAmB;MACvC,OAAO;QACL,GAAGhC,KAAK;QACRX,cAAc,EAAE;UACd,GAAGW,KAAK,CAACX,cAAc;UACvBvB,OAAO,EAAE,IAAI;UACbI,KAAK,EAAE;QACT;MACF,CAAC;IAEH,KAAKR,gBAAgB,CAACuE,2BAA2B;MAC/C,OAAO;QACL,GAAGjC,KAAK;QACRX,cAAc,EAAE;UACd,GAAGW,KAAK,CAACX,cAAc;UACvBvB,OAAO,EAAE,KAAK;UACdwB,IAAI,EAAEW,MAAM,CAACI,OAAO,CAACf,IAAI,CAAC4C,KAAK,IAAI,EAAE;UACrC/D,UAAU,EAAE;YACV,GAAG6B,KAAK,CAACX,cAAc,CAAClB,UAAU;YAClC,GAAG8B,MAAM,CAACI,OAAO,CAACf,IAAI,CAACnB;UACzB,CAAC;UACDO,OAAO,EAAE;YACP,GAAGsB,KAAK,CAACX,cAAc,CAACX,OAAO;YAC/B,GAAGuB,MAAM,CAACI,OAAO,CAACf,IAAI,CAACZ;UACzB,CAAC;UACDR,KAAK,EAAE;QACT;MACF,CAAC;IAEH,KAAKR,gBAAgB,CAACyE,2BAA2B;MAC/C,OAAO;QACL,GAAGnC,KAAK;QACRX,cAAc,EAAE;UACd,GAAGW,KAAK,CAACX,cAAc;UACvBvB,OAAO,EAAE,KAAK;UACdI,KAAK,EAAE+B,MAAM,CAACI;QAChB;MACF,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAAC0E,mBAAmB;MACvC,OAAO;QACL,GAAGpC,KAAK;QACRR,cAAc,EAAE;UACd,GAAGQ,KAAK,CAACR,cAAc;UACvB1B,OAAO,EAAE,IAAI;UACbI,KAAK,EAAE;QACT;MACF,CAAC;IAEH,KAAKR,gBAAgB,CAAC2E,2BAA2B;MAC/C,OAAO;QACL,GAAGrC,KAAK;QACRR,cAAc,EAAE;UACd,GAAGQ,KAAK,CAACR,cAAc;UACvB1B,OAAO,EAAE,KAAK;UACdwB,IAAI,EAAEW,MAAM,CAACI,OAAO,CAACf,IAAI,CAAC1B,UAAU,IAAI,EAAE;UAC1C6B,IAAI,EAAEQ,MAAM,CAACI,OAAO,CAACf,IAAI,CAACG,IAAI;UAC9BtB,UAAU,EAAE;YACV,GAAG6B,KAAK,CAACR,cAAc,CAACrB,UAAU;YAClC,GAAG8B,MAAM,CAACI,OAAO,CAACf,IAAI,CAACnB;UACzB,CAAC;UACDD,KAAK,EAAE;QACT;MACF,CAAC;IAEH,KAAKR,gBAAgB,CAAC4E,2BAA2B;MAC/C,OAAO;QACL,GAAGtC,KAAK;QACRR,cAAc,EAAE;UACd,GAAGQ,KAAK,CAACR,cAAc;UACvB1B,OAAO,EAAE,KAAK;UACdI,KAAK,EAAE+B,MAAM,CAACI;QAChB;MACF,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAAC6E,0BAA0B;MAC9C,OAAO;QACL,GAAGvC,KAAK;QACRN,YAAY,EAAE,IAAI;QAClBxB,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC8E,kCAAkC;MACtD,OAAO;QACL,GAAGxC,KAAK;QACRN,YAAY,EAAE,KAAK;QACnB;QACAL,cAAc,EAAE;UACd,GAAGW,KAAK,CAACX,cAAc;UACvBC,IAAI,EAAEU,KAAK,CAACX,cAAc,CAACC,IAAI,CAAC6B,MAAM,CACpC1B,IAAI,IAAIA,IAAI,CAACsB,GAAG,KAAKd,MAAM,CAACI,OAAO,CAACoC,aACtC;QACF,CAAC;QACDvE,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACgF,kCAAkC;MACtD,OAAO;QACL,GAAG1C,KAAK;QACRN,YAAY,EAAE,KAAK;QACnBxB,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAACiF,0BAA0B;MAC9C,OAAO;QACL,GAAG3C,KAAK;QACRL,cAAc,EAAE,IAAI;QACpBzB,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACkF,kCAAkC;MACtD,OAAO;QACL,GAAG5C,KAAK;QACRL,cAAc,EAAE,KAAK;QACrB;QACAN,cAAc,EAAE;UACd,GAAGW,KAAK,CAACX,cAAc;UACvBC,IAAI,EAAEU,KAAK,CAACX,cAAc,CAACC,IAAI,CAACuB,GAAG,CAACpB,IAAI,IACtCA,IAAI,CAACsB,GAAG,KAAKd,MAAM,CAACI,OAAO,CAACf,IAAI,CAACuD,aAAa,CAAC9B,GAAG,GAC9C;YAAE,GAAGtB,IAAI;YAAEqD,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAK,CAAC,GAC3CtD,IACN;QACF,CAAC;QACDvB,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACsF,kCAAkC;MACtD,OAAO;QACL,GAAGhD,KAAK;QACRL,cAAc,EAAE,KAAK;QACrBzB,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;;IAEH;;IAEA;IACA,KAAK3C,gBAAgB,CAACuF,2BAA2B;MAC/C,OAAO;QACL,GAAGjD,KAAK;QACRJ,WAAW,EAAE;UACX,GAAGI,KAAK,CAACJ,WAAW;UACpB9B,OAAO,EAAE,IAAI;UACbI,KAAK,EAAE;QACT;MACF,CAAC;IAEH,KAAKR,gBAAgB,CAACwF,mCAAmC;MACvD,OAAO;QACL,GAAGlD,KAAK;QACRJ,WAAW,EAAE;UACX,GAAGI,KAAK,CAACJ,WAAW;UACpB9B,OAAO,EAAE,KAAK;UACd+B,cAAc,EAAEI,MAAM,CAACI,OAAO,CAACf,IAAI,CAAC4C,KAAK,IAAI,EAAE;UAC/C/D,UAAU,EAAE;YACV,GAAG6B,KAAK,CAACJ,WAAW,CAACzB,UAAU;YAC/B,GAAG8B,MAAM,CAACI,OAAO,CAACf,IAAI,CAACnB;UACzB,CAAC;UACDO,OAAO,EAAE;YACP,GAAGsB,KAAK,CAACJ,WAAW,CAAClB,OAAO;YAC5B,GAAGuB,MAAM,CAACI,OAAO,CAACf,IAAI,CAACZ;UACzB,CAAC;UACDR,KAAK,EAAE;QACT;MACF,CAAC;IAEH,KAAKR,gBAAgB,CAACyF,mCAAmC;MACvD,OAAO;QACL,GAAGnD,KAAK;QACRJ,WAAW,EAAE;UACX,GAAGI,KAAK,CAACJ,WAAW;UACpB9B,OAAO,EAAE,KAAK;UACdI,KAAK,EAAE+B,MAAM,CAACI;QAChB;MACF,CAAC;;IAEH;IACA,KAAK3C,gBAAgB,CAAC0F,yBAAyB;MAC7C,OAAO;QACL,GAAGpD,KAAK;QACRF,cAAc,EAAE,IAAI;QACpB5B,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAAC2F,iCAAiC;MACrD,OAAO;QACL,GAAGrD,KAAK;QACRF,cAAc,EAAE,KAAK;QACrB;QACAF,WAAW,EAAE;UACX,GAAGI,KAAK,CAACJ,WAAW;UACpBC,cAAc,EAAEG,KAAK,CAACJ,WAAW,CAACC,cAAc,CAACgB,GAAG,CAACpB,IAAI,IAAI;YAC3D,MAAM6D,WAAW,GAAGrD,MAAM,CAACI,OAAO,CAACf,IAAI,CAACiE,aAAa,CAACC,IAAI,CACxDC,YAAY,IAAIA,YAAY,CAAC1C,GAAG,KAAKtB,IAAI,CAACsB,GAC5C,CAAC;YACD,OAAOuC,WAAW,GAAG;cAAE,GAAG7D,IAAI;cAAEiE,UAAU,EAAE;YAAK,CAAC,GAAGjE,IAAI;UAC3D,CAAC;QACH,CAAC;QACDvB,KAAK,EAAE;MACT,CAAC;IAEH,KAAKR,gBAAgB,CAACiG,iCAAiC;MACrD,OAAO;QACL,GAAG3D,KAAK;QACRF,cAAc,EAAE,KAAK;QACrB5B,KAAK,EAAE+B,MAAM,CAACI;MAChB,CAAC;IAEH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,eAAeD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}