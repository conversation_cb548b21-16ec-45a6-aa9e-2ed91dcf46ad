const mongoose = require('mongoose');
require('dotenv').config();

// Import User model
const User = require('./src/models/user');

async function checkAdminUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
    console.log('🔗 Connected to MongoDB');
    
    // Find all admin users
    const adminUsers = await User.find({ role: 'ADMIN' }).select('_id name email role isVerified isLocked');
    
    console.log('\n📋 ADMIN USERS IN DATABASE:');
    console.log('=' .repeat(50));
    
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found in database');
      console.log('\n💡 To create an admin user, you can:');
      console.log('1. Register a new user normally');
      console.log('2. Manually update their role to "ADMIN" in the database');
      console.log('3. Or create a script to add admin users');
    } else {
      adminUsers.forEach((user, index) => {
        console.log(`\n${index + 1}. Admin User:`);
        console.log(`   ID: ${user._id}`);
        console.log(`   Name: ${user.name}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Verified: ${user.isVerified ? '✅' : '❌'}`);
        console.log(`   Locked: ${user.isLocked ? '🔒' : '🔓'}`);
      });
    }
    
    // Also check all users to see their roles
    const allUsers = await User.find({}).select('_id name email role').limit(10);
    console.log('\n📋 SAMPLE USERS (first 10):');
    console.log('=' .repeat(50));
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email}) - Role: ${user.role}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkAdminUsers();
