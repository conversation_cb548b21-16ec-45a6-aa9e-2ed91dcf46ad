const jwt = require("jsonwebtoken");

const isAdmin = (req, res, next) => {
  console.log('🔍 isAdmin middleware called for:', req.method, req.originalUrl);

  const token = req.headers.authorization?.split(" ")[1];
  if (!token) {
    console.log('❌ No token provided');
    return res.status(401).json({ message: "No token provided" });
  }

  jwt.verify(token, process.env.SECRET_KEY, (err, decoded) => {
    if (err) {
      console.log('❌ Token verification failed:', err.message);
      return res.status(403).json({ message: "Invalid token" });
    }

    console.log('🔍 Token decoded, user role:', decoded.user?.role);

    if (decoded.user.role !== "ADMIN") {
      console.log('❌ Access denied, user is not admin');
      return res.status(403).json({ message: "Access denied, You don't have admin role" });
    }

    console.log('✅ Admin access granted');
    req.user = decoded.user;
    next();
  });
};

module.exports = { isAdmin };
