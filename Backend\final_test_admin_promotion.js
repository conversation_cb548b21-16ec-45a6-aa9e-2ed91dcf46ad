const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function finalTestAdminPromotion() {
  try {
    console.log('🎯 FINAL TEST: ADMIN PROMOTION MANAGEMENT\n');
    console.log('=' .repeat(70));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin Authentication...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    const user = loginResponse.data.Data.user;
    
    console.log(`✅ Login successful: ${user.name} (${user.role})`);
    
    // Step 2: Test default admin view (should use status sorting)
    console.log('\n2️⃣ Testing Default Admin View...');
    const defaultResponse = await axios.get(`${BASE_URL}/promotions?sortBy=status&sortOrder=asc`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const promotions = defaultResponse.data.promotions;
    console.log(`📋 Total promotions visible to admin: ${promotions.length}`);
    
    // Step 3: Analyze visibility (PUBLIC vs PRIVATE)
    const publicPromotions = promotions.filter(p => p.type === 'PUBLIC');
    const privatePromotions = promotions.filter(p => p.type === 'PRIVATE');
    
    console.log('\n3️⃣ Visibility Analysis:');
    console.log(`🌍 PUBLIC promotions: ${publicPromotions.length}`);
    console.log(`🔒 PRIVATE promotions: ${privatePromotions.length}`);
    console.log(`✅ Admin can see both PUBLIC and PRIVATE: ${privatePromotions.length > 0 ? 'YES' : 'NO'}`);
    
    // Step 4: Analyze sorting order
    console.log('\n4️⃣ Sorting Order Analysis:');
    const now = new Date();
    let currentPriority = 0;
    let sortingCorrect = true;
    const statusCounts = { active: 0, upcoming: 0, inactive: 0, expired: 0 };
    
    console.log('📊 Promotion Order (First 10):');
    promotions.slice(0, 10).forEach((promo, index) => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      let status = '';
      let icon = '';
      let priority = 0;
      
      if (!promo.isActive) {
        status = 'INACTIVE';
        icon = '⚫';
        priority = 3;
        statusCounts.inactive++;
      } else if (now > endDate) {
        status = 'EXPIRED';
        icon = '⏰';
        priority = 4;
        statusCounts.expired++;
      } else if (now < startDate) {
        status = 'COMING SOON';
        icon = '⏳';
        priority = 2;
        statusCounts.upcoming++;
      } else {
        status = 'ACTIVE';
        icon = '✅';
        priority = 1;
        statusCounts.active++;
      }
      
      if (priority < currentPriority) {
        sortingCorrect = false;
      }
      currentPriority = priority;
      
      console.log(`${index + 1}. ${icon} ${promo.code} (${promo.type}) - ${status}`);
    });
    
    // Step 5: Summary
    console.log('\n5️⃣ Test Results Summary:');
    console.log('=' .repeat(70));
    
    console.log(`📊 Status Distribution:`);
    console.log(`   ✅ Active: ${statusCounts.active}`);
    console.log(`   ⏳ Coming Soon: ${statusCounts.upcoming}`);
    console.log(`   ⚫ Inactive: ${statusCounts.inactive}`);
    console.log(`   ⏰ Expired: ${statusCounts.expired}`);
    
    console.log(`\n🔍 Visibility Test:`);
    console.log(`   🌍 PUBLIC visible: ✅ YES (${publicPromotions.length} promotions)`);
    console.log(`   🔒 PRIVATE visible: ${privatePromotions.length > 0 ? '✅ YES' : '❌ NO'} (${privatePromotions.length} promotions)`);
    
    console.log(`\n📈 Sorting Test:`);
    console.log(`   Order: ${sortingCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
    console.log(`   Expected: Active → Coming Soon → Inactive → Expired`);
    
    // Step 6: Final verdict
    console.log('\n🎯 FINAL VERDICT:');
    console.log('=' .repeat(70));
    
    const allTestsPassed = privatePromotions.length > 0 && sortingCorrect;
    
    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED!');
      console.log('✅ Admin can see all promotions (PUBLIC + PRIVATE)');
      console.log('✅ Promotions are sorted correctly by status');
      console.log('✅ Ready for production use!');
    } else {
      console.log('❌ SOME TESTS FAILED!');
      if (privatePromotions.length === 0) {
        console.log('❌ Admin cannot see PRIVATE promotions');
      }
      if (!sortingCorrect) {
        console.log('❌ Sorting order is incorrect');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.response?.data || error.message);
  }
}

finalTestAdminPromotion();
