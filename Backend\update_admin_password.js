const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('./src/models/user');

async function updateAdminPassword() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
    console.log('🔗 Connected to MongoDB');
    
    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('📋 Current admin user info:');
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Verified: ${adminUser.isVerified}`);
    console.log(`   Locked: ${adminUser.isLocked}`);
    
    // Update password to 'admin123' (will be auto-hashed by pre-save middleware)
    const newPassword = 'admin123';

    console.log(`🔧 Setting raw password: ${newPassword}`);
    console.log('📝 Note: Password will be auto-hashed by User model pre-save middleware');

    adminUser.password = newPassword; // Set raw password, let middleware hash it
    adminUser.isVerified = true; // Make sure admin is verified
    adminUser.isLocked = false; // Make sure admin is not locked

    await adminUser.save();

    // Verify the save worked
    const updatedUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
    const finalTest = await bcrypt.compare(newPassword, updatedUser.password);
    console.log(`🔍 Final verification: ${finalTest ? '✅ Password saved correctly' : '❌ Password save failed'}`);
    console.log(`🔧 Final password hash: ${updatedUser.password.substring(0, 20)}...`);
    
    console.log('\n✅ Admin user updated successfully!');
    console.log(`🔑 New password: ${newPassword}`);
    console.log('✅ Admin is now verified and unlocked');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

updateAdminPassword();
