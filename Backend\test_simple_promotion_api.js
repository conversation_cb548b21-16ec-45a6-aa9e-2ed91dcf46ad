const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testSimplePromotionAPI() {
  try {
    console.log('🧪 TESTING SIMPLE PROMOTION API\n');
    console.log('=' .repeat(50));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    console.log('✅ Login successful');
    
    // Step 2: Test basic promotion API
    console.log('\n2️⃣ Testing basic promotion API...');
    const promotionsResponse = await axios.get(`${BASE_URL}/promotions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ Basic promotion API works: ${promotionsResponse.status}`);
    const promotions = promotionsResponse.data.promotions;
    const privatePromotion = promotions.find(p => p.type === 'PRIVATE');
    
    if (!privatePromotion) {
      console.log('❌ No PRIVATE promotion found');
      return;
    }
    
    console.log(`📋 Found PRIVATE promotion: ${privatePromotion.code} (ID: ${privatePromotion._id})`);
    
    // Step 3: Test promotion users API (should work)
    console.log('\n3️⃣ Testing promotion users API...');
    try {
      const usersResponse = await axios.get(`${BASE_URL}/promotions/${privatePromotion._id}/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ Promotion users API works: ${usersResponse.status}`);
    } catch (error) {
      console.log(`❌ Promotion users API failed: ${error.response?.status} - ${error.response?.data?.message}`);
    }
    
    // Step 4: Test search users API (the problematic one)
    console.log('\n4️⃣ Testing search users API...');
    try {
      const searchResponse = await axios.get(`${BASE_URL}/promotions/${privatePromotion._id}/search-users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ Search users API works: ${searchResponse.status}`);
      console.log('Response data:', searchResponse.data);
    } catch (error) {
      console.log(`❌ Search users API failed: ${error.response?.status} - ${error.response?.data?.message}`);
      console.log('Full error:', error.response?.data);
    }
    
    // Step 5: Test assign users API
    console.log('\n5️⃣ Testing assign users API...');
    try {
      const assignResponse = await axios.post(`${BASE_URL}/promotions/${privatePromotion._id}/assign-users`, {
        userIds: ['507f1f77bcf86cd799439011'] // Fake user ID for testing
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ Assign users API works: ${assignResponse.status}`);
    } catch (error) {
      console.log(`❌ Assign users API failed: ${error.response?.status} - ${error.response?.data?.message}`);
      console.log('Full error:', error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testSimplePromotionAPI();
