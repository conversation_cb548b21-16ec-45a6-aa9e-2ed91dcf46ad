const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('./src/models/user');

async function checkAdminPassword() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/My_Uroom');
    console.log('🔗 Connected to MongoDB');
    
    // Find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
    
    if (!adminUser) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('📋 Admin user info:');
    console.log(`   Name: ${adminUser.name}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Verified: ${adminUser.isVerified}`);
    console.log(`   Locked: ${adminUser.isLocked}`);
    console.log(`   Password hash: ${adminUser.password.substring(0, 20)}...`);
    
    // Test different passwords
    const testPasswords = ['admin123', '123456', 'password', 'admin', 'uroom123'];
    
    console.log('\n🔍 Testing passwords:');
    for (const password of testPasswords) {
      const isMatch = await bcrypt.compare(password, adminUser.password);
      console.log(`   ${password}: ${isMatch ? '✅ MATCH' : '❌ No match'}`);
      if (isMatch) {
        console.log(`🎉 Found working password: ${password}`);
        break;
      }
    }
    
    // Also try to create a new password hash and compare
    console.log('\n🔧 Creating new hash for "admin123":');
    const newHash = await bcrypt.hash('admin123', 10);
    console.log(`   New hash: ${newHash.substring(0, 20)}...`);
    const testNew = await bcrypt.compare('admin123', newHash);
    console.log(`   Test new hash: ${testNew ? '✅ Works' : '❌ Failed'}`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkAdminPassword();
