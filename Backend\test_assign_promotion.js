const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAssignPromotion() {
  try {
    console.log('🧪 TESTING ASSIGN PROMOTION FUNCTIONALITY\n');
    console.log('=' .repeat(70));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    console.log('✅ Login successful');
    
    // Step 2: Get all promotions to find a PRIVATE one
    console.log('\n2️⃣ Finding PRIVATE promotions...');
    const promotionsResponse = await axios.get(`${BASE_URL}/promotions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const promotions = promotionsResponse.data.promotions;
    const privatePromotions = promotions.filter(p => p.type === 'PRIVATE');
    
    if (privatePromotions.length === 0) {
      console.log('❌ No PRIVATE promotions found for testing');
      return;
    }
    
    const testPromotion = privatePromotions[0];
    console.log(`📋 Using promotion: ${testPromotion.code} (${testPromotion.name})`);
    console.log(`🔍 Promotion ID: ${testPromotion._id}`);
    console.log(`🔍 Promotion Type: ${testPromotion.type}`);

    // Step 3: Search for users to assign
    console.log('\n3️⃣ Searching for users to assign...');
    console.log(`🔗 API URL: ${BASE_URL}/promotions/${testPromotion._id}/search-users?page=1&limit=5`);

    const searchUsersResponse = await axios.get(`${BASE_URL}/promotions/${testPromotion._id}/search-users?page=1&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ Search users API status: ${searchUsersResponse.status}`);
    const searchData = searchUsersResponse.data;
    
    if (searchData.data && searchData.data.availableUsers) {
      const availableUsers = searchData.data.availableUsers;
      console.log(`📋 Found ${availableUsers.length} available users`);
      
      // Show first few users
      availableUsers.slice(0, 3).forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (${user.email}) - Assigned: ${user.isAssigned ? 'Yes' : 'No'}`);
      });
      
      // Step 4: Try to assign promotion to first available user
      const unassignedUsers = availableUsers.filter(user => !user.isAssigned);
      
      if (unassignedUsers.length > 0) {
        const userToAssign = unassignedUsers[0];
        console.log(`\n4️⃣ Assigning promotion to user: ${userToAssign.name}`);
        
        const assignResponse = await axios.post(`${BASE_URL}/promotions/${testPromotion._id}/assign-users`, {
          userIds: [userToAssign._id]
        }, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`✅ Assign API status: ${assignResponse.status}`);
        const assignData = assignResponse.data;
        
        console.log('📊 Assignment result:');
        console.log(`   Success: ${assignData.success}`);
        console.log(`   Message: ${assignData.message}`);
        
        if (assignData.data) {
          console.log(`   Assigned users: ${assignData.data.assignedUsers.length}`);
          console.log(`   Skipped users: ${assignData.data.skippedUsers.length}`);
          
          assignData.data.assignedUsers.forEach(user => {
            console.log(`   ✅ Assigned: ${user.name} (${user.email})`);
          });
        }
        
        console.log('\n🎉 ASSIGN PROMOTION TEST SUCCESSFUL!');
        
      } else {
        console.log('\n⚠️  No unassigned users found for testing');
      }
      
    } else {
      console.log('❌ Unexpected search users response format');
      console.log('Response:', searchData);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      console.log('\n💡 Possible issues:');
      console.log('   - API endpoint not found');
      console.log('   - Route not properly configured');
    } else if (error.response?.status === 401) {
      console.log('\n💡 Possible issues:');
      console.log('   - Admin authentication failed');
      console.log('   - Token expired or invalid');
    } else if (error.response?.status === 400) {
      console.log('\n💡 Possible issues:');
      console.log('   - Invalid request data');
      console.log('   - Promotion type not PRIVATE');
      console.log('   - User IDs invalid');
    }
  }
}

testAssignPromotion();
