const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAdminPromotionAccess() {
  try {
    console.log('🧪 TESTING ADMIN PROMOTION ACCESS\n');
    console.log('=' .repeat(50));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    const user = loginResponse.data.Data.user;
    
    console.log(`✅ Login successful: ${user.name} (${user.role})`);
    
    // Step 2: Test promotion API with admin token
    console.log('\n2️⃣ Fetching promotions with admin token...');
    const promotionResponse = await axios.get(`${BASE_URL}/promotions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ API Status: ${promotionResponse.status}`);
    
    const responseData = promotionResponse.data;
    console.log(`📊 Response structure:`, Object.keys(responseData));
    
    if (responseData.data && Array.isArray(responseData.data)) {
      const promotions = responseData.data;
      console.log(`📋 Total promotions: ${promotions.length}`);
      
      // Count by type
      const publicCount = promotions.filter(p => p.type === 'PUBLIC').length;
      const privateCount = promotions.filter(p => p.type === 'PRIVATE').length;
      
      console.log(`🌍 Public promotions: ${publicCount}`);
      console.log(`🔒 Private promotions: ${privateCount}`);
      
      if (privateCount > 0) {
        console.log('\n🎉 SUCCESS! Admin can see private promotions!');
        
        // Show some private promotions
        const privatePromotions = promotions.filter(p => p.type === 'PRIVATE');
        console.log('\n🔒 Private promotions found:');
        privatePromotions.slice(0, 3).forEach((promo, index) => {
          console.log(`${index + 1}. ${promo.name} (${promo.code}) - ${promo.type}`);
        });
      } else {
        console.log('\n⚠️  No private promotions found in response');
      }
      
      // Show all promotions
      console.log('\n📋 All promotions:');
      promotions.forEach((promo, index) => {
        console.log(`${index + 1}. ${promo.name} (${promo.code}) - ${promo.type}`);
      });
      
    } else {
      console.log('❌ Unexpected response format');
      console.log('Response:', responseData);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testAdminPromotionAccess();
