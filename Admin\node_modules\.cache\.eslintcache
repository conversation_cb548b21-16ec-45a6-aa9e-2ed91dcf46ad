[{"E:\\Uroom\\Admin\\src\\index.js": "1", "E:\\Uroom\\Admin\\src\\App.js": "2", "E:\\Uroom\\Admin\\src\\reportWebVitals.js": "3", "E:\\Uroom\\Admin\\src\\redux\\store.js": "4", "E:\\Uroom\\Admin\\src\\utils\\Routes.js": "5", "E:\\Uroom\\Admin\\src\\redux\\socket\\socketSlice.js": "6", "E:\\Uroom\\Admin\\src\\redux\\root-reducer.js": "7", "E:\\Uroom\\Admin\\src\\redux\\root-saga.js": "8", "E:\\Uroom\\Admin\\src\\pages\\BannedPage.jsx": "9", "E:\\Uroom\\Admin\\src\\pages\\DashboardAdmin.jsx": "10", "E:\\Uroom\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx": "11", "E:\\Uroom\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx": "12", "E:\\Uroom\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx": "13", "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx": "14", "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx": "15", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx": "16", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx": "17", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx": "18", "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx": "19", "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx": "20", "E:\\Uroom\\Admin\\src\\pages\\login_register\\RegisterPage.jsx": "21", "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx": "22", "E:\\Uroom\\Admin\\src\\pages\\login_register\\LoginPage.jsx": "23", "E:\\Uroom\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx": "24", "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx": "25", "E:\\Uroom\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx": "26", "E:\\Uroom\\Admin\\src\\redux\\feedback\\saga.js": "27", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\saga.js": "28", "E:\\Uroom\\Admin\\src\\redux\\auth\\saga.js": "29", "E:\\Uroom\\Admin\\src\\redux\\auth\\reducer.js": "30", "E:\\Uroom\\Admin\\src\\redux\\auth\\actions.js": "31", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\reducer.js": "32", "E:\\Uroom\\Admin\\src\\utils\\handleToken.js": "33", "E:\\Uroom\\Admin\\src\\redux\\feedback\\actions.js": "34", "E:\\Uroom\\Admin\\src\\redux\\feedback\\reducer.js": "35", "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx": "36", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\actions.js": "37", "E:\\Uroom\\Admin\\src\\redux\\promotion\\reducer.js": "38", "E:\\Uroom\\Admin\\src\\redux\\message\\reducer.js": "39", "E:\\Uroom\\Admin\\src\\redux\\message\\saga.js": "40", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\saga.js": "41", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\reducer.js": "42", "E:\\Uroom\\Admin\\src\\redux\\promotion\\saga.js": "43", "E:\\Uroom\\Admin\\src\\pages\\SidebarAdmin.jsx": "44", "E:\\Uroom\\Admin\\src\\redux\\auth\\factories.js": "45", "E:\\Uroom\\Admin\\src\\components\\ToastContainer.jsx": "46", "E:\\Uroom\\Admin\\src\\adapter\\ApiConstants.js": "47", "E:\\Uroom\\Admin\\src\\components\\ConfirmationModal.jsx": "48", "E:\\Uroom\\Admin\\src\\pages\\approve\\ApprovePage.jsx": "49", "E:\\Uroom\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx": "50", "E:\\Uroom\\Admin\\src\\pages\\messenger\\Chat.jsx": "51", "E:\\Uroom\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx": "52", "E:\\Uroom\\Admin\\src\\utils\\Utils.js": "53", "E:\\Uroom\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx": "54", "E:\\Uroom\\Admin\\src\\libs\\api\\index.js": "55", "E:\\Uroom\\Admin\\src\\redux\\feedback\\factories.js": "56", "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\factories.js": "57", "E:\\Uroom\\Admin\\src\\redux\\message\\actions.js": "58", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\actions.js": "59", "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\factories.js": "60", "E:\\Uroom\\Admin\\src\\redux\\message\\factories.js": "61", "E:\\Uroom\\Admin\\src\\redux\\promotion\\factories.js": "62", "E:\\Uroom\\Admin\\src\\utils\\fonts.js": "63", "E:\\Uroom\\Admin\\src\\redux\\promotion\\actions.js": "64", "E:\\Uroom\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx": "65", "E:\\Uroom\\Admin\\src\\libs\\firebaseConfig.js": "66", "E:\\Uroom\\Admin\\src\\pages\\promotion\\PromotionUsersModal.jsx": "67", "E:\\Uroom\\Admin\\src\\pages\\promotion\\AssignPromotionModal.jsx": "68"}, {"size": 839, "mtime": 1753036043087, "results": "69", "hashOfConfig": "70"}, {"size": 4119, "mtime": 1753036043054, "results": "71", "hashOfConfig": "70"}, {"size": 375, "mtime": 1753036043106, "results": "72", "hashOfConfig": "70"}, {"size": 1291, "mtime": 1753036043106, "results": "73", "hashOfConfig": "70"}, {"size": 1302, "mtime": 1753036043107, "results": "74", "hashOfConfig": "70"}, {"size": 1451, "mtime": 1753036043106, "results": "75", "hashOfConfig": "70"}, {"size": 733, "mtime": 1753036043105, "results": "76", "hashOfConfig": "70"}, {"size": 568, "mtime": 1753036043106, "results": "77", "hashOfConfig": "70"}, {"size": 1474, "mtime": 1753036043088, "results": "78", "hashOfConfig": "70"}, {"size": 16320, "mtime": 1753036043089, "results": "79", "hashOfConfig": "70"}, {"size": 10290, "mtime": 1753036043092, "results": "80", "hashOfConfig": "70"}, {"size": 14538, "mtime": 1753036043090, "results": "81", "hashOfConfig": "70"}, {"size": 22581, "mtime": 1753036043090, "results": "82", "hashOfConfig": "70"}, {"size": 15326, "mtime": 1753036043097, "results": "83", "hashOfConfig": "70"}, {"size": 6292, "mtime": 1753036043098, "results": "84", "hashOfConfig": "70"}, {"size": 25537, "mtime": 1753036043092, "results": "85", "hashOfConfig": "70"}, {"size": 1723, "mtime": 1753036043092, "results": "86", "hashOfConfig": "70"}, {"size": 18937, "mtime": 1753036043093, "results": "87", "hashOfConfig": "70"}, {"size": 24809, "mtime": 1753036043096, "results": "88", "hashOfConfig": "70"}, {"size": 3050, "mtime": 1753036043093, "results": "89", "hashOfConfig": "70"}, {"size": 7403, "mtime": 1753036043094, "results": "90", "hashOfConfig": "70"}, {"size": 7520, "mtime": 1753036043094, "results": "91", "hashOfConfig": "70"}, {"size": 15641, "mtime": 1753036043094, "results": "92", "hashOfConfig": "70"}, {"size": 3718, "mtime": 1753036043093, "results": "93", "hashOfConfig": "70"}, {"size": 8490, "mtime": 1753036043095, "results": "94", "hashOfConfig": "70"}, {"size": 5423, "mtime": 1753036043094, "results": "95", "hashOfConfig": "70"}, {"size": 1225, "mtime": 1753036043100, "results": "96", "hashOfConfig": "70"}, {"size": 2761, "mtime": 1753036043105, "results": "97", "hashOfConfig": "70"}, {"size": 11749, "mtime": 1753036043099, "results": "98", "hashOfConfig": "70"}, {"size": 2374, "mtime": 1753036043099, "results": "99", "hashOfConfig": "70"}, {"size": 1424, "mtime": 1753036043099, "results": "100", "hashOfConfig": "70"}, {"size": 955, "mtime": 1753036043105, "results": "101", "hashOfConfig": "70"}, {"size": 551, "mtime": 1753036043107, "results": "102", "hashOfConfig": "70"}, {"size": 196, "mtime": 1753036043099, "results": "103", "hashOfConfig": "70"}, {"size": 764, "mtime": 1753036043100, "results": "104", "hashOfConfig": "70"}, {"size": 24271, "mtime": 1753036043096, "results": "105", "hashOfConfig": "70"}, {"size": 466, "mtime": 1753036043105, "results": "106", "hashOfConfig": "70"}, {"size": 12438, "mtime": 1753113445163, "results": "107", "hashOfConfig": "70"}, {"size": 551, "mtime": 1753036043100, "results": "108", "hashOfConfig": "70"}, {"size": 2085, "mtime": 1753036043100, "results": "109", "hashOfConfig": "70"}, {"size": 1477, "mtime": 1753036043098, "results": "110", "hashOfConfig": "70"}, {"size": 1558, "mtime": 1753036043098, "results": "111", "hashOfConfig": "70"}, {"size": 14937, "mtime": 1753111545364, "results": "112", "hashOfConfig": "70"}, {"size": 2422, "mtime": 1753036043089, "results": "113", "hashOfConfig": "70"}, {"size": 1617, "mtime": 1753036043099, "results": "114", "hashOfConfig": "70"}, {"size": 1493, "mtime": 1753036043055, "results": "115", "hashOfConfig": "70"}, {"size": 4228, "mtime": 1753111614536, "results": "116", "hashOfConfig": "70"}, {"size": 2348, "mtime": 1753036043055, "results": "117", "hashOfConfig": "70"}, {"size": 6543, "mtime": 1753036043089, "results": "118", "hashOfConfig": "70"}, {"size": 52967, "mtime": 1753036043091, "results": "119", "hashOfConfig": "70"}, {"size": 25958, "mtime": 1753036043095, "results": "120", "hashOfConfig": "70"}, {"size": 35796, "mtime": 1753113698080, "results": "121", "hashOfConfig": "70"}, {"size": 3227, "mtime": 1753036043107, "results": "122", "hashOfConfig": "70"}, {"size": 2140, "mtime": 1753036043093, "results": "123", "hashOfConfig": "70"}, {"size": 2658, "mtime": 1753036043087, "results": "124", "hashOfConfig": "70"}, {"size": 342, "mtime": 1753036043100, "results": "125", "hashOfConfig": "70"}, {"size": 595, "mtime": 1753036043105, "results": "126", "hashOfConfig": "70"}, {"size": 322, "mtime": 1753036043100, "results": "127", "hashOfConfig": "70"}, {"size": 887, "mtime": 1753036043098, "results": "128", "hashOfConfig": "70"}, {"size": 283, "mtime": 1753036043098, "results": "129", "hashOfConfig": "70"}, {"size": 377, "mtime": 1753036043100, "results": "130", "hashOfConfig": "70"}, {"size": 4351, "mtime": 1753111557949, "results": "131", "hashOfConfig": "70"}, {"size": 636, "mtime": 1753036043107, "results": "132", "hashOfConfig": "70"}, {"size": 8164, "mtime": 1753111402127, "results": "133", "hashOfConfig": "70"}, {"size": 21444, "mtime": 1753111376797, "results": "134", "hashOfConfig": "70"}, {"size": 693, "mtime": 1753036043088, "results": "135", "hashOfConfig": "70"}, {"size": 14634, "mtime": 1753114440311, "results": "136", "hashOfConfig": "70"}, {"size": 11000, "mtime": 1753111122522, "results": "137", "hashOfConfig": "70"}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tmwm7w", {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Uroom\\Admin\\src\\index.js", [], [], "E:\\Uroom\\Admin\\src\\App.js", ["342"], [], "E:\\Uroom\\Admin\\src\\reportWebVitals.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\store.js", [], [], "E:\\Uroom\\Admin\\src\\utils\\Routes.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\socket\\socketSlice.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\root-reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\root-saga.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\BannedPage.jsx", ["343"], [], "E:\\Uroom\\Admin\\src\\pages\\DashboardAdmin.jsx", ["344", "345", "346", "347", "348", "349", "350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360", "361"], [], "E:\\Uroom\\Admin\\src\\pages\\feedback\\ListFeedbackAdminPage.jsx", ["362", "363", "364", "365"], [], "E:\\Uroom\\Admin\\src\\pages\\customer\\DetailCustomerAdmin.jsx", ["366", "367", "368", "369"], [], "E:\\Uroom\\Admin\\src\\pages\\customer\\ListCustomerAdmin.jsx", ["370", "371", "372", "373", "374"], [], "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\DetailReportedAdmin.jsx", ["375", "376"], [], "E:\\Uroom\\Admin\\src\\pages\\reported_feedback\\ReportedFeedbackAdmin.jsx", ["377", "378", "379"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\DetailHotelHostAdmin.jsx", ["380", "381", "382", "383"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\ApprovalAccountHotelhost.jsx", ["384", "385"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\HotelManagement.jsx", ["386", "387", "388", "389"], [], "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentHotel.jsx", ["390", "391", "392", "393"], [], "E:\\Uroom\\Admin\\src\\pages\\hotelHost\\TransactionHotelhost.jsx", ["394", "395"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\RegisterPage.jsx", ["396", "397"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodePage.jsx", ["398", "399", "400", "401", "402"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\LoginPage.jsx", ["403"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\ForgetPasswordPage.jsx", ["404", "405", "406", "407", "408"], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\VerifyCodeRegisterPage.jsx", [], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\ResetPasswordPage.jsx", ["409", "410"], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\utils\\handleToken.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\payment\\ListPaymentCustomer.jsx", ["411", "412"], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\reducer.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\saga.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\SidebarAdmin.jsx", ["413", "414", "415", "416", "417", "418"], [], "E:\\Uroom\\Admin\\src\\redux\\auth\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\components\\ToastContainer.jsx", [], [], "E:\\Uroom\\Admin\\src\\adapter\\ApiConstants.js", ["419", "420", "421"], [], "E:\\Uroom\\Admin\\src\\components\\ConfirmationModal.jsx", [], [], "E:\\Uroom\\Admin\\src\\pages\\approve\\ApprovePage.jsx", ["422", "423", "424", "425", "426"], [], "E:\\Uroom\\Admin\\src\\pages\\dashboard\\DashboardPage.jsx", ["427"], [], "E:\\Uroom\\Admin\\src\\pages\\messenger\\Chat.jsx", ["428", "429", "430", "431", "432", "433", "434", "435"], [], "E:\\Uroom\\Admin\\src\\pages\\promotion\\ListPromotionPage.jsx", ["436", "437"], [], "E:\\Uroom\\Admin\\src\\utils\\Utils.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\login_register\\GoogleLogin.jsx", [], [], "E:\\Uroom\\Admin\\src\\libs\\api\\index.js", ["438"], [], "E:\\Uroom\\Admin\\src\\redux\\feedback\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\reportedFeedback\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\adminDashboard\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\message\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\factories.js", [], [], "E:\\Uroom\\Admin\\src\\utils\\fonts.js", [], [], "E:\\Uroom\\Admin\\src\\redux\\promotion\\actions.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\promotion\\DetailPromotionPage.jsx", ["439"], [], "E:\\Uroom\\Admin\\src\\libs\\firebaseConfig.js", [], [], "E:\\Uroom\\Admin\\src\\pages\\promotion\\PromotionUsersModal.jsx", ["440", "441", "442", "443", "444", "445", "446"], [], "E:\\Uroom\\Admin\\src\\pages\\promotion\\AssignPromotionModal.jsx", ["447", "448", "449", "450"], [], {"ruleId": "451", "severity": 1, "message": "452", "line": 40, "column": 6, "nodeType": "453", "endLine": 40, "endColumn": 17, "suggestions": "454"}, {"ruleId": "455", "severity": 1, "message": "456", "line": 1, "column": 17, "nodeType": "457", "messageId": "458", "endLine": 1, "endColumn": 26}, {"ruleId": "455", "severity": 1, "message": "459", "line": 4, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 14}, {"ruleId": "455", "severity": 1, "message": "460", "line": 4, "column": 16, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 19}, {"ruleId": "455", "severity": 1, "message": "461", "line": 4, "column": 21, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 24}, {"ruleId": "455", "severity": 1, "message": "462", "line": 4, "column": 26, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 34}, {"ruleId": "455", "severity": 1, "message": "463", "line": 21, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 21, "endColumn": 25}, {"ruleId": "455", "severity": 1, "message": "464", "line": 28, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 28, "endColumn": 29}, {"ruleId": "455", "severity": 1, "message": "465", "line": 66, "column": 28, "nodeType": "457", "messageId": "458", "endLine": 66, "endColumn": 47}, {"ruleId": "455", "severity": 1, "message": "466", "line": 68, "column": 25, "nodeType": "457", "messageId": "458", "endLine": 68, "endColumn": 41}, {"ruleId": "467", "severity": 1, "message": "468", "line": 179, "column": 19, "nodeType": "469", "endLine": 179, "endColumn": 76}, {"ruleId": "467", "severity": 1, "message": "468", "line": 189, "column": 19, "nodeType": "469", "endLine": 189, "endColumn": 78}, {"ruleId": "467", "severity": 1, "message": "468", "line": 199, "column": 19, "nodeType": "469", "endLine": 199, "endColumn": 76}, {"ruleId": "467", "severity": 1, "message": "468", "line": 209, "column": 19, "nodeType": "469", "endLine": 209, "endColumn": 77}, {"ruleId": "467", "severity": 1, "message": "468", "line": 219, "column": 19, "nodeType": "469", "endLine": 219, "endColumn": 76}, {"ruleId": "467", "severity": 1, "message": "468", "line": 234, "column": 19, "nodeType": "469", "endLine": 234, "endColumn": 75}, {"ruleId": "467", "severity": 1, "message": "468", "line": 244, "column": 19, "nodeType": "469", "endLine": 244, "endColumn": 84}, {"ruleId": "467", "severity": 1, "message": "468", "line": 254, "column": 19, "nodeType": "469", "endLine": 254, "endColumn": 74}, {"ruleId": "467", "severity": 1, "message": "468", "line": 269, "column": 19, "nodeType": "469", "endLine": 269, "endColumn": 76}, {"ruleId": "467", "severity": 1, "message": "470", "line": 377, "column": 25, "nodeType": "469", "endLine": 392, "endColumn": 26}, {"ruleId": "455", "severity": 1, "message": "471", "line": 9, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 9, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "472", "line": 18, "column": 13, "nodeType": "457", "messageId": "458", "endLine": 18, "endColumn": 20}, {"ruleId": "455", "severity": 1, "message": "473", "line": 23, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 23, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "474", "line": 24, "column": 19, "nodeType": "457", "messageId": "458", "endLine": 24, "endColumn": 29}, {"ruleId": "455", "severity": 1, "message": "456", "line": 1, "column": 20, "nodeType": "457", "messageId": "458", "endLine": 1, "endColumn": 29}, {"ruleId": "455", "severity": 1, "message": "475", "line": 16, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 16, "endColumn": 16}, {"ruleId": "455", "severity": 1, "message": "476", "line": 41, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 41, "endColumn": 25}, {"ruleId": "455", "severity": 1, "message": "477", "line": 50, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 50, "endColumn": 27}, {"ruleId": "455", "severity": 1, "message": "476", "line": 29, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 29, "endColumn": 25}, {"ruleId": "455", "severity": 1, "message": "478", "line": 49, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 49, "endColumn": 21}, {"ruleId": "455", "severity": 1, "message": "479", "line": 52, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 52, "endColumn": 21}, {"ruleId": "455", "severity": 1, "message": "480", "line": 120, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 120, "endColumn": 23}, {"ruleId": "455", "severity": 1, "message": "477", "line": 150, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 150, "endColumn": 27}, {"ruleId": "455", "severity": 1, "message": "481", "line": 8, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 8, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "471", "line": 9, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 9, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "482", "line": 5, "column": 25, "nodeType": "457", "messageId": "458", "endLine": 5, "endColumn": 34}, {"ruleId": "451", "severity": 1, "message": "483", "line": 36, "column": 6, "nodeType": "453", "endLine": 36, "endColumn": 16, "suggestions": "484"}, {"ruleId": "455", "severity": 1, "message": "485", "line": 86, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 86, "endColumn": 20}, {"ruleId": "455", "severity": 1, "message": "486", "line": 2, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 2, "endColumn": 12}, {"ruleId": "455", "severity": 1, "message": "487", "line": 22, "column": 29, "nodeType": "457", "messageId": "458", "endLine": 22, "endColumn": 40}, {"ruleId": "455", "severity": 1, "message": "473", "line": 59, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 59, "endColumn": 17}, {"ruleId": "467", "severity": 1, "message": "470", "line": 531, "column": 31, "nodeType": "469", "endLine": 531, "endColumn": 34}, {"ruleId": "455", "severity": 1, "message": "472", "line": 2, "column": 13, "nodeType": "457", "messageId": "458", "endLine": 2, "endColumn": 20}, {"ruleId": "455", "severity": 1, "message": "473", "line": 6, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 6, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "472", "line": 6, "column": 13, "nodeType": "457", "messageId": "458", "endLine": 6, "endColumn": 20}, {"ruleId": "455", "severity": 1, "message": "473", "line": 29, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 29, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "488", "line": 31, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 31, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "480", "line": 116, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 116, "endColumn": 23}, {"ruleId": "451", "severity": 1, "message": "489", "line": 300, "column": 6, "nodeType": "453", "endLine": 300, "endColumn": 51, "suggestions": "490"}, {"ruleId": "467", "severity": 1, "message": "468", "line": 515, "column": 19, "nodeType": "469", "endLine": 522, "endColumn": 20}, {"ruleId": "467", "severity": 1, "message": "468", "line": 535, "column": 23, "nodeType": "469", "endLine": 542, "endColumn": 24}, {"ruleId": "467", "severity": 1, "message": "468", "line": 550, "column": 19, "nodeType": "469", "endLine": 557, "endColumn": 20}, {"ruleId": "455", "severity": 1, "message": "491", "line": 1, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 1, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "471", "line": 1, "column": 29, "nodeType": "457", "messageId": "458", "endLine": 1, "endColumn": 39}, {"ruleId": "455", "severity": 1, "message": "487", "line": 4, "column": 29, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 40}, {"ruleId": "467", "severity": 1, "message": "470", "line": 215, "column": 17, "nodeType": "469", "endLine": 219, "endColumn": 52}, {"ruleId": "455", "severity": 1, "message": "487", "line": 3, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 3, "endColumn": 21}, {"ruleId": "455", "severity": 1, "message": "492", "line": 10, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 10, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "493", "line": 22, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 22, "endColumn": 19}, {"ruleId": "455", "severity": 1, "message": "494", "line": 27, "column": 17, "nodeType": "457", "messageId": "458", "endLine": 27, "endColumn": 25}, {"ruleId": "467", "severity": 1, "message": "468", "line": 203, "column": 17, "nodeType": "469", "endLine": 203, "endColumn": 89}, {"ruleId": "455", "severity": 1, "message": "495", "line": 13, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 13, "endColumn": 19}, {"ruleId": "455", "severity": 1, "message": "487", "line": 4, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 21}, {"ruleId": "455", "severity": 1, "message": "496", "line": 10, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 10, "endColumn": 19}, {"ruleId": "455", "severity": 1, "message": "492", "line": 12, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 12, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "497", "line": 15, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 15, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "498", "line": 63, "column": 9, "nodeType": "457", "messageId": "458", "endLine": 63, "endColumn": 33}, {"ruleId": "455", "severity": 1, "message": "487", "line": 4, "column": 29, "nodeType": "457", "messageId": "458", "endLine": 4, "endColumn": 40}, {"ruleId": "455", "severity": 1, "message": "499", "line": 6, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 6, "endColumn": 15}, {"ruleId": "455", "severity": 1, "message": "492", "line": 30, "column": 8, "nodeType": "457", "messageId": "458", "endLine": 30, "endColumn": 13}, {"ruleId": "451", "severity": 1, "message": "500", "line": 69, "column": 6, "nodeType": "453", "endLine": 69, "endColumn": 8, "suggestions": "501"}, {"ruleId": "467", "severity": 1, "message": "470", "line": 19, "column": 11, "nodeType": "469", "endLine": 24, "endColumn": 12}, {"ruleId": "467", "severity": 1, "message": "470", "line": 30, "column": 11, "nodeType": "469", "endLine": 35, "endColumn": 12}, {"ruleId": "467", "severity": 1, "message": "470", "line": 41, "column": 11, "nodeType": "469", "endLine": 46, "endColumn": 12}, {"ruleId": "467", "severity": 1, "message": "470", "line": 52, "column": 11, "nodeType": "469", "endLine": 57, "endColumn": 12}, {"ruleId": "467", "severity": 1, "message": "470", "line": 63, "column": 11, "nodeType": "469", "endLine": 68, "endColumn": 12}, {"ruleId": "467", "severity": 1, "message": "470", "line": 73, "column": 11, "nodeType": "469", "endLine": 78, "endColumn": 12}, {"ruleId": "502", "severity": 1, "message": "503", "line": 10, "column": 3, "nodeType": "504", "messageId": "505", "endLine": 10, "endColumn": 17}, {"ruleId": "502", "severity": 1, "message": "506", "line": 11, "column": 3, "nodeType": "504", "messageId": "505", "endLine": 11, "endColumn": 18}, {"ruleId": "502", "severity": 1, "message": "507", "line": 48, "column": 3, "nodeType": "504", "messageId": "505", "endLine": 48, "endColumn": 28}, {"ruleId": "467", "severity": 1, "message": "468", "line": 171, "column": 15, "nodeType": "469", "endLine": 171, "endColumn": 49}, {"ruleId": "467", "severity": 1, "message": "468", "line": 176, "column": 15, "nodeType": "469", "endLine": 176, "endColumn": 49}, {"ruleId": "467", "severity": 1, "message": "468", "line": 181, "column": 15, "nodeType": "469", "endLine": 181, "endColumn": 49}, {"ruleId": "467", "severity": 1, "message": "468", "line": 186, "column": 15, "nodeType": "469", "endLine": 186, "endColumn": 49}, {"ruleId": "467", "severity": 1, "message": "468", "line": 191, "column": 15, "nodeType": "469", "endLine": 191, "endColumn": 49}, {"ruleId": "455", "severity": 1, "message": "460", "line": 2, "column": 16, "nodeType": "457", "messageId": "458", "endLine": 2, "endColumn": 19}, {"ruleId": "455", "severity": 1, "message": "508", "line": 10, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 10, "endColumn": 26}, {"ruleId": "455", "severity": 1, "message": "509", "line": 33, "column": 10, "nodeType": "457", "messageId": "458", "endLine": 33, "endColumn": 20}, {"ruleId": "451", "severity": 1, "message": "510", "line": 80, "column": 6, "nodeType": "453", "endLine": 80, "endColumn": 8, "suggestions": "511"}, {"ruleId": "451", "severity": 1, "message": "512", "line": 84, "column": 6, "nodeType": "453", "endLine": 84, "endColumn": 20, "suggestions": "513"}, {"ruleId": "514", "severity": 1, "message": "515", "line": 119, "column": 29, "nodeType": "516", "messageId": "505", "endLine": 119, "endColumn": 31}, {"ruleId": "451", "severity": 1, "message": "510", "line": 142, "column": 6, "nodeType": "453", "endLine": 142, "endColumn": 44, "suggestions": "517"}, {"ruleId": "514", "severity": 1, "message": "515", "line": 417, "column": 46, "nodeType": "516", "messageId": "505", "endLine": 417, "endColumn": 48}, {"ruleId": "514", "severity": 1, "message": "515", "line": 430, "column": 38, "nodeType": "516", "messageId": "505", "endLine": 430, "endColumn": 40}, {"ruleId": "455", "severity": 1, "message": "518", "line": 16, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 16, "endColumn": 11}, {"ruleId": "455", "severity": 1, "message": "519", "line": 24, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 24, "endColumn": 11}, {"ruleId": "520", "severity": 1, "message": "521", "line": 37, "column": 1, "nodeType": "522", "endLine": 108, "endColumn": 3}, {"ruleId": "455", "severity": 1, "message": "523", "line": 21, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 21, "endColumn": 9}, {"ruleId": "455", "severity": 1, "message": "524", "line": 14, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 14, "endColumn": 7}, {"ruleId": "455", "severity": 1, "message": "518", "line": 16, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 16, "endColumn": 11}, {"ruleId": "455", "severity": 1, "message": "519", "line": 22, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 22, "endColumn": 11}, {"ruleId": "455", "severity": 1, "message": "525", "line": 24, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 24, "endColumn": 13}, {"ruleId": "455", "severity": 1, "message": "526", "line": 30, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 30, "endColumn": 8}, {"ruleId": "455", "severity": 1, "message": "527", "line": 31, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 31, "endColumn": 10}, {"ruleId": "451", "severity": 1, "message": "528", "line": 63, "column": 6, "nodeType": "453", "endLine": 63, "endColumn": 32, "suggestions": "529"}, {"ruleId": "455", "severity": 1, "message": "530", "line": 15, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 15, "endColumn": 17}, {"ruleId": "455", "severity": 1, "message": "531", "line": 16, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 16, "endColumn": 10}, {"ruleId": "455", "severity": 1, "message": "527", "line": 22, "column": 3, "nodeType": "457", "messageId": "458", "endLine": 22, "endColumn": 10}, {"ruleId": "451", "severity": 1, "message": "532", "line": 45, "column": 6, "nodeType": "453", "endLine": 45, "endColumn": 48, "suggestions": "533"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", "ArrayExpression", ["534"], "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Line' is defined but never used.", "'Bar' is defined but never used.", "'Pie' is defined but never used.", "'Doughnut' is defined but never used.", "'AccountManagement' is defined but never used.", "'ListFeedbackAdminPage' is defined but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "'setNotifications' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'InputGroup' is defined but never used.", "'Routers' is defined but never used.", "'navigate' is assigned a value but never used.", "'setReviews' is assigned a value but never used.", "'useRef' is defined but never used.", "'showDeleteModal' is assigned a value but never used.", "'handleLockCustomer' is assigned a value but never used.", "'handleAccept' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'Pagination' is defined but never used.", "'showToast' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadReportedFeedbacks'. Either include it or remove the dependency array.", ["535"], "'getSeverity' is assigned a value but never used.", "'Container' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["536"], "'Col' is defined but never used.", "'axios' is defined but never used.", "'isLoading' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'GoogleLogin' is defined but never used.", "'AuthActions' is defined but never used.", "'dispatch' is assigned a value but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'Route' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchRefunds'. Either include it or remove the dependency array.", ["537"], "no-dupe-keys", "Duplicate key 'UPDATE_PROFILE'.", "ObjectExpression", "unexpected", "Duplicate key 'CHANGE_PASSWORD'.", "Duplicate key 'FETCH_FEEDBACK_BY_HOTELID'.", "'initializeSocket' is defined but never used.", "'isReadLast' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllUser'. Either include it or remove the dependency array.", ["538"], "React Hook useEffect has a missing dependency: 'fetchHistoryChat'. Either include it or remove the dependency array.", ["539"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", ["540"], "'Dropdown' is defined but never used.", "'FaFilter' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'FaEdit' is defined but never used.", "'Card' is defined but never used.", "'FaCalendar' is defined but never used.", "'FaEye' is defined but never used.", "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPromotionUsers'. Either include it or remove the dependency array.", ["541"], "'OverlayTrigger' is defined but never used.", "'Tooltip' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["542"], {"desc": "543", "fix": "544"}, {"desc": "545", "fix": "546"}, {"desc": "547", "fix": "548"}, {"desc": "549", "fix": "550"}, {"desc": "551", "fix": "552"}, {"desc": "553", "fix": "554"}, {"desc": "555", "fix": "556"}, {"desc": "557", "fix": "558"}, {"desc": "559", "fix": "560"}, "Update the dependencies array to be: [Auth?._id, dispatch]", {"range": "561", "text": "562"}, "Update the dependencies array to be: [dispatch, loadReportedFeedbacks]", {"range": "563", "text": "564"}, "Update the dependencies array to be: [selected<PERSON><PERSON><PERSON>, selected<PERSON>ear, selectedStatus, fetchPayments]", {"range": "565", "text": "566"}, "Update the dependencies array to be: [fetchRefunds]", {"range": "567", "text": "568"}, "Update the dependencies array to be: [fetchAllUser]", {"range": "569", "text": "570"}, "Update the dependencies array to be: [fetchHistoryChat, selectedUser]", {"range": "571", "text": "572"}, "Update the dependencies array to be: [Socket, Auth._id, selectedUser?._id, fetchAllUser]", {"range": "573", "text": "574"}, "Update the dependencies array to be: [show, promotion, filters, loadPromotionUsers]", {"range": "575", "text": "576"}, "Update the dependencies array to be: [show, promotion, searchTerm, currentPage, loadUsers]", {"range": "577", "text": "578"}, [1916, 1927], "[Auth?._id, dispatch]", [1162, 1172], "[dispatch, loadReportedFeedbacks]", [11004, 11049], "[selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, selected<PERSON>tatus, fetchPayments]", [1864, 1866], "[fetchRefunds]", [2772, 2774], "[fetchAllUser]", [2831, 2845], "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>]", [4480, 4518], "[Socket, Auth._id, selectedUser?._id, fetchAllUser]", [1354, 1380], "[show, promotion, filters, loadPromotionUsers]", [945, 987], "[show, promotion, searchTerm, currentPage, loadUsers]"]