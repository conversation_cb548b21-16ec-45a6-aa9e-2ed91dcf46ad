const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testAdminSorting() {
  try {
    console.log('🧪 TESTING ADMIN PROMOTION SORTING\n');
    console.log('=' .repeat(60));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    const user = loginResponse.data.Data.user;
    
    console.log(`✅ Login successful: ${user.name} (${user.role})`);
    
    // Step 2: Test default sorting (should be status-asc)
    console.log('\n2️⃣ Testing default sorting (status-asc)...');
    const defaultResponse = await axios.get(`${BASE_URL}/promotions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ API Status: ${defaultResponse.status}`);
    const defaultPromotions = defaultResponse.data.promotions;
    console.log(`📋 Total promotions: ${defaultPromotions.length}`);
    
    // Analyze sorting
    console.log('\n📊 Promotion Status Analysis:');
    const now = new Date();
    
    defaultPromotions.forEach((promo, index) => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      let status = '';
      let icon = '';
      
      if (!promo.isActive) {
        status = 'INACTIVE';
        icon = '⚫';
      } else if (now > endDate) {
        status = 'EXPIRED';
        icon = '⏰';
      } else if (now < startDate) {
        status = 'COMING SOON';
        icon = '⏳';
      } else {
        status = 'ACTIVE';
        icon = '✅';
      }
      
      console.log(`${index + 1}. ${icon} ${promo.code} - ${promo.name}`);
      console.log(`   Type: ${promo.type} | Status: ${status}`);
      console.log(`   Start: ${startDate.toLocaleDateString()} | End: ${endDate.toLocaleDateString()}`);
      console.log('');
    });
    
    // Step 3: Test explicit status sorting
    console.log('\n3️⃣ Testing explicit status sorting...');
    const statusResponse = await axios.get(`${BASE_URL}/promotions?sortBy=status&sortOrder=asc`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const statusPromotions = statusResponse.data.promotions;
    console.log(`📋 Status-sorted promotions: ${statusPromotions.length}`);
    
    // Count by status
    const statusCounts = {
      active: 0,
      upcoming: 0,
      inactive: 0,
      expired: 0
    };
    
    statusPromotions.forEach(promo => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      if (!promo.isActive) {
        statusCounts.inactive++;
      } else if (now > endDate) {
        statusCounts.expired++;
      } else if (now < startDate) {
        statusCounts.upcoming++;
      } else {
        statusCounts.active++;
      }
    });
    
    console.log('\n📈 Status Distribution:');
    console.log(`✅ Active: ${statusCounts.active}`);
    console.log(`⏳ Coming Soon: ${statusCounts.upcoming}`);
    console.log(`⚫ Inactive: ${statusCounts.inactive}`);
    console.log(`⏰ Expired: ${statusCounts.expired}`);
    
    // Step 4: Test type distribution
    const publicCount = statusPromotions.filter(p => p.type === 'PUBLIC').length;
    const privateCount = statusPromotions.filter(p => p.type === 'PRIVATE').length;
    
    console.log('\n🔍 Type Distribution:');
    console.log(`🌍 PUBLIC: ${publicCount}`);
    console.log(`🔒 PRIVATE: ${privateCount}`);
    
    console.log('\n🎉 SUCCESS! Admin can see all promotions with proper sorting!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testAdminSorting();
