const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testStatusSorting() {
  try {
    console.log('🧪 TESTING STATUS SORTING ONLY\n');
    console.log('=' .repeat(60));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    console.log('✅ Login successful');
    
    // Step 2: Test explicit status sorting
    console.log('\n2️⃣ Testing explicit status sorting (sortBy=status&sortOrder=asc)...');
    const response = await axios.get(`${BASE_URL}/promotions?sortBy=status&sortOrder=asc&limit=20`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ API Status: ${response.status}`);
    const promotions = response.data.promotions;
    console.log(`📋 Total promotions: ${promotions.length}`);
    
    // Analyze and display sorted order
    console.log('\n📊 SORTED PROMOTION ORDER:');
    console.log('=' .repeat(60));
    
    const now = new Date();
    const statusGroups = {
      active: [],
      upcoming: [],
      inactive: [],
      expired: []
    };
    
    promotions.forEach((promo, index) => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      let status = '';
      let icon = '';
      let group = '';
      
      if (!promo.isActive) {
        status = 'INACTIVE';
        icon = '⚫';
        group = 'inactive';
      } else if (now > endDate) {
        status = 'EXPIRED';
        icon = '⏰';
        group = 'expired';
      } else if (now < startDate) {
        status = 'COMING SOON';
        icon = '⏳';
        group = 'upcoming';
      } else {
        status = 'ACTIVE';
        icon = '✅';
        group = 'active';
      }
      
      statusGroups[group].push(promo);
      
      console.log(`${index + 1}. ${icon} ${promo.code} - ${promo.name}`);
      console.log(`   Type: ${promo.type} | Status: ${status}`);
      console.log('');
    });
    
    // Display group analysis
    console.log('\n📈 STATUS GROUP ANALYSIS:');
    console.log('=' .repeat(60));
    console.log(`✅ ACTIVE: ${statusGroups.active.length} promotions`);
    statusGroups.active.forEach(p => console.log(`   - ${p.code} (${p.type})`));
    
    console.log(`\n⏳ COMING SOON: ${statusGroups.upcoming.length} promotions`);
    statusGroups.upcoming.forEach(p => console.log(`   - ${p.code} (${p.type})`));
    
    console.log(`\n⚫ INACTIVE: ${statusGroups.inactive.length} promotions`);
    statusGroups.inactive.forEach(p => console.log(`   - ${p.code} (${p.type})`));
    
    console.log(`\n⏰ EXPIRED: ${statusGroups.expired.length} promotions`);
    statusGroups.expired.forEach(p => console.log(`   - ${p.code} (${p.type})`));
    
    // Check if sorting is correct
    console.log('\n🔍 SORTING VERIFICATION:');
    console.log('=' .repeat(60));
    
    let currentPriority = 0;
    let sortingCorrect = true;
    
    promotions.forEach((promo, index) => {
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      let priority = 0;
      if (!promo.isActive) priority = 3; // inactive
      else if (now > endDate) priority = 4; // expired
      else if (now < startDate) priority = 2; // upcoming
      else priority = 1; // active
      
      if (priority < currentPriority) {
        sortingCorrect = false;
      }
      currentPriority = priority;
    });
    
    if (sortingCorrect) {
      console.log('✅ Sorting is CORRECT! Active → Coming Soon → Inactive → Expired');
    } else {
      console.log('❌ Sorting is INCORRECT! Order is mixed');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testStatusSorting();
