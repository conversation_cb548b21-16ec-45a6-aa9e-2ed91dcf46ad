// Debug script to check promotion counts directly from database
const mongoose = require('mongoose');
require('dotenv').config();

console.log('🔍 Environment check:');
console.log('ENVIRONMENT:', process.env.ENVIRONMENT);
console.log('MONGODB_URI_DEVELOPMENT:', process.env.MONGODB_URI_DEVELOPMENT);
console.log('MONGODB_URI_PRODUCTION:', process.env.MONGODB_URI_PRODUCTION);

// Determine correct MongoDB URI based on environment
const environment = process.env.ENVIRONMENT || 'development';
let mongoUri;

if (environment === 'production') {
  mongoUri = process.env.MONGODB_URI_PRODUCTION;
} else {
  mongoUri = process.env.MONGODB_URI_DEVELOPMENT;
}

// Fallback
if (!mongoUri) {
  mongoUri = 'mongodb://localhost:27017/uroom';
  console.log('⚠️ No environment-specific URI found, using fallback');
}

console.log('🔗 Environment:', environment);
console.log('🔗 Connecting to:', mongoUri);

mongoose.connect(mongoUri);

// Connection events
mongoose.connection.on('connected', () => {
  console.log('✅ MongoDB connected successfully');
});

mongoose.connection.on('error', (err) => {
  console.error('❌ MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('📡 MongoDB disconnected');
});

// Import models
const Promotion = require('./src/models/Promotion');

async function debugPromotions() {
  try {
    console.log('\n🔍 PROMOTION DEBUG ANALYSIS\n');
    console.log('=' .repeat(50));

    // Wait for connection
    await new Promise(resolve => {
      if (mongoose.connection.readyState === 1) {
        resolve();
      } else {
        mongoose.connection.once('connected', resolve);
      }
    });

    // Check database info
    const db = mongoose.connection.db;
    console.log('📊 Database name:', db.databaseName);

    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('📋 Available collections:', collections.map(c => c.name));

    // Check if promotions collection exists
    const hasPromotions = collections.some(c => c.name === 'promotions');
    console.log('🎯 Promotions collection exists:', hasPromotions);

    if (hasPromotions) {
      // Check collection stats
      const stats = await db.collection('promotions').stats();
      console.log('📈 Promotions collection stats:', {
        count: stats.count,
        size: stats.size,
        avgObjSize: stats.avgObjSize
      });
    }

    // 1. Total count
    const totalCount = await Promotion.countDocuments();
    console.log(`\n📊 Total promotions in database: ${totalCount}`);

    // 2. Count by type
    const publicCount = await Promotion.countDocuments({ type: 'PUBLIC' });
    const privateCount = await Promotion.countDocuments({ type: 'PRIVATE' });
    console.log(`📋 PUBLIC promotions: ${publicCount}`);
    console.log(`📋 PRIVATE promotions: ${privateCount}`);

    // 3. Count by status
    const now = new Date();
    const activeCount = await Promotion.countDocuments({
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now }
    });
    const inactiveCount = await Promotion.countDocuments({ isActive: false });
    const expiredCount = await Promotion.countDocuments({ endDate: { $lt: now } });
    const upcomingCount = await Promotion.countDocuments({
      isActive: true,
      startDate: { $gt: now }
    });

    console.log(`\n📈 Status breakdown:`);
    console.log(`   Active: ${activeCount}`);
    console.log(`   Inactive: ${inactiveCount}`);
    console.log(`   Expired: ${expiredCount}`);
    console.log(`   Upcoming: ${upcomingCount}`);

    // 4. Detailed breakdown
    console.log(`\n📋 Detailed breakdown:`);
    
    const publicActive = await Promotion.countDocuments({
      type: 'PUBLIC',
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now }
    });
    const privateActive = await Promotion.countDocuments({
      type: 'PRIVATE',
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now }
    });

    console.log(`   PUBLIC + Active: ${publicActive}`);
    console.log(`   PRIVATE + Active: ${privateActive}`);

    // 5. Sample promotions
    console.log(`\n📋 Sample promotions:`);
    const samplePromotions = await Promotion.find()
      .select('code name type isActive startDate endDate')
      .limit(10)
      .sort({ createdAt: -1 });

    samplePromotions.forEach((promo, index) => {
      const status = now < new Date(promo.startDate) ? 'upcoming' :
                    now > new Date(promo.endDate) ? 'expired' :
                    !promo.isActive ? 'inactive' : 'active';
      
      console.log(`   ${index + 1}. ${promo.code} (${promo.type}) - ${status}`);
    });

    // 6. Test filters that admin would use
    console.log(`\n🔍 Testing admin filters:`);
    
    // Filter 1: All promotions (what admin should see)
    const adminAllFilter = {};
    const adminAllCount = await Promotion.countDocuments(adminAllFilter);
    console.log(`   Admin "all" filter: ${adminAllCount} promotions`);

    // Filter 2: Customer filter (PUBLIC only)
    const customerFilter = { type: 'PUBLIC' };
    const customerCount = await Promotion.countDocuments(customerFilter);
    console.log(`   Customer filter (PUBLIC only): ${customerCount} promotions`);

    // Filter 3: Admin with status=all (should be same as adminAllFilter)
    const adminStatusAllFilter = {}; // No additional filters for status=all
    const adminStatusAllCount = await Promotion.countDocuments(adminStatusAllFilter);
    console.log(`   Admin status="all": ${adminStatusAllCount} promotions`);

    console.log('\n' + '=' .repeat(50));
    console.log('✅ Debug analysis complete!');

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run debug
debugPromotions();
