// Script to check both development and production databases
const mongoose = require('mongoose');
require('dotenv').config();

const Promotion = require('./src/models/Promotion');

async function checkDatabase(uri, name) {
  console.log(`\n🔍 CHECKING ${name.toUpperCase()} DATABASE`);
  console.log('=' .repeat(50));
  console.log('URI:', uri);

  try {
    // Create new connection for this database
    const connection = await mongoose.createConnection(uri);
    
    console.log('✅ Connected successfully');
    console.log('📊 Database name:', connection.db.databaseName);

    // List collections
    const collections = await connection.db.listCollections().toArray();
    console.log('📋 Collections:', collections.map(c => c.name));

    // Check promotions collection
    const hasPromotions = collections.some(c => c.name === 'promotions');
    console.log('🎯 Has promotions collection:', hasPromotions);

    if (hasPromotions) {
      // Use the connection to create a model
      const PromotionModel = connection.model('Promotion', Promotion.schema);
      
      const totalCount = await PromotionModel.countDocuments();
      console.log('📊 Total promotions:', totalCount);

      if (totalCount > 0) {
        // Count by type
        const publicCount = await PromotionModel.countDocuments({ type: 'PUBLIC' });
        const privateCount = await PromotionModel.countDocuments({ type: 'PRIVATE' });
        console.log('📋 PUBLIC promotions:', publicCount);
        console.log('📋 PRIVATE promotions:', privateCount);

        // Sample promotions
        const samples = await PromotionModel.find()
          .select('code name type isActive')
          .limit(5)
          .sort({ createdAt: -1 });
        
        console.log('📋 Sample promotions:');
        samples.forEach((promo, index) => {
          console.log(`   ${index + 1}. ${promo.code} (${promo.type}) - ${promo.isActive ? 'Active' : 'Inactive'}`);
        });
      }
    }

    await connection.close();
    console.log('📡 Connection closed');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function main() {
  console.log('🧪 DATABASE COMPARISON TOOL\n');
  
  const devUri = process.env.MONGODB_URI_DEVELOPMENT;
  const prodUri = process.env.MONGODB_URI_PRODUCTION;
  const environment = process.env.ENVIRONMENT;

  console.log('🔍 Environment variables:');
  console.log('ENVIRONMENT:', environment);
  console.log('DEV URI:', devUri ? 'Set' : 'Not set');
  console.log('PROD URI:', prodUri ? 'Set' : 'Not set');

  if (devUri) {
    await checkDatabase(devUri, 'development');
  }

  if (prodUri) {
    await checkDatabase(prodUri, 'production');
  }

  console.log('\n' + '=' .repeat(50));
  console.log(`✅ Current environment: ${environment}`);
  console.log(`🎯 Server will use: ${environment === 'production' ? 'PRODUCTION' : 'DEVELOPMENT'} database`);
  console.log('✅ Database comparison complete!');
}

main().catch(console.error);
