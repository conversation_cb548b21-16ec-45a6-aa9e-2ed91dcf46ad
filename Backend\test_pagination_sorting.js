const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testPaginationSorting() {
  try {
    console.log('🧪 TESTING PAGINATION WITH SORTING\n');
    console.log('=' .repeat(70));
    
    // Step 1: Login as admin
    console.log('1️⃣ Admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login_customer`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.Data.token;
    console.log('✅ Login successful');
    
    // Step 2: Test page 1 with status sorting
    console.log('\n2️⃣ Testing Page 1 (sortBy=status, limit=5)...');
    const page1Response = await axios.get(`${BASE_URL}/promotions?sortBy=status&sortOrder=asc&page=1&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const page1Promotions = page1Response.data.promotions;
    const pagination1 = page1Response.data.pagination;
    
    console.log(`📋 Page 1: ${page1Promotions.length} promotions`);
    console.log(`📊 Pagination: Page ${pagination1.currentPage}/${pagination1.totalPages} (Total: ${pagination1.totalPromotions})`);
    
    console.log('\n📋 Page 1 Promotions:');
    page1Promotions.forEach((promo, index) => {
      const now = new Date();
      const startDate = new Date(promo.startDate);
      const endDate = new Date(promo.endDate);
      
      let status = '';
      let icon = '';
      
      if (!promo.isActive) {
        status = 'INACTIVE';
        icon = '⚫';
      } else if (now > endDate) {
        status = 'EXPIRED';
        icon = '⏰';
      } else if (now < startDate) {
        status = 'COMING SOON';
        icon = '⏳';
      } else {
        status = 'ACTIVE';
        icon = '✅';
      }
      
      console.log(`${index + 1}. ${icon} ${promo.code} (${promo.type}) - ${status}`);
    });
    
    // Step 3: Test page 2 with same sorting
    if (pagination1.totalPages > 1) {
      console.log('\n3️⃣ Testing Page 2 (same sorting)...');
      const page2Response = await axios.get(`${BASE_URL}/promotions?sortBy=status&sortOrder=asc&page=2&limit=5`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      const page2Promotions = page2Response.data.promotions;
      const pagination2 = page2Response.data.pagination;
      
      console.log(`📋 Page 2: ${page2Promotions.length} promotions`);
      console.log(`📊 Pagination: Page ${pagination2.currentPage}/${pagination2.totalPages} (Total: ${pagination2.totalPromotions})`);
      
      console.log('\n📋 Page 2 Promotions:');
      page2Promotions.forEach((promo, index) => {
        const now = new Date();
        const startDate = new Date(promo.startDate);
        const endDate = new Date(promo.endDate);
        
        let status = '';
        let icon = '';
        
        if (!promo.isActive) {
          status = 'INACTIVE';
          icon = '⚫';
        } else if (now > endDate) {
          status = 'EXPIRED';
          icon = '⏰';
        } else if (now < startDate) {
          status = 'COMING SOON';
          icon = '⏳';
        } else {
          status = 'ACTIVE';
          icon = '✅';
        }
        
        console.log(`${index + 1}. ${icon} ${promo.code} (${promo.type}) - ${status}`);
      });
      
      // Step 4: Verify sorting consistency across pages
      console.log('\n4️⃣ Verifying sorting consistency...');
      
      // Check if last item of page 1 has same or higher priority than first item of page 2
      if (page1Promotions.length > 0 && page2Promotions.length > 0) {
        const lastPage1 = page1Promotions[page1Promotions.length - 1];
        const firstPage2 = page2Promotions[0];
        
        const getStatusPriority = (promo) => {
          const now = new Date();
          const startDate = new Date(promo.startDate);
          const endDate = new Date(promo.endDate);
          
          if (!promo.isActive) return 3; // inactive
          if (now > endDate) return 4; // expired
          if (now < startDate) return 2; // upcoming
          return 1; // active
        };
        
        const lastPriority = getStatusPriority(lastPage1);
        const firstPriority = getStatusPriority(firstPage2);
        
        console.log(`Last item of Page 1: ${lastPage1.code} (Priority: ${lastPriority})`);
        console.log(`First item of Page 2: ${firstPage2.code} (Priority: ${firstPriority})`);
        
        if (lastPriority <= firstPriority) {
          console.log('✅ Sorting is CONSISTENT across pages!');
        } else {
          console.log('❌ Sorting is INCONSISTENT across pages!');
        }
      }
    } else {
      console.log('\n3️⃣ Only one page available, pagination test skipped.');
    }
    
    // Step 5: Test with different sorting
    console.log('\n5️⃣ Testing different sorting (createdAt-desc)...');
    const createdAtResponse = await axios.get(`${BASE_URL}/promotions?sortBy=createdAt&sortOrder=desc&page=1&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const createdAtPromotions = createdAtResponse.data.promotions;
    console.log(`📋 CreatedAt sorting: ${createdAtPromotions.length} promotions`);
    
    console.log('\n📋 CreatedAt Sorted Promotions:');
    createdAtPromotions.forEach((promo, index) => {
      const createdDate = new Date(promo.createdAt).toLocaleDateString();
      console.log(`${index + 1}. ${promo.code} (${promo.type}) - Created: ${createdDate}`);
    });
    
    console.log('\n🎯 PAGINATION SORTING TEST COMPLETE!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testPaginationSorting();
